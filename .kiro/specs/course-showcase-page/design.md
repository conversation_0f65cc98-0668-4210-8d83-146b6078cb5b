# Design Document

## Overview

The course showcase page for "教育人 7天咨询起盘营" will be a single-page application that presents <PERSON>'s expertise, course positioning, and interactive curriculum in a warm, professional, and engaging manner. The design follows the "遇见之屿" brand guidelines with a focus on creating trust through visual hierarchy, emotional connection through warm colors, and functionality through integrated audio playback.

The page will use a progressive disclosure approach, allowing users to explore content at their own pace while maintaining easy access to audio materials. The design emphasizes the transformation journey from education professional to psychological consultant, using visual metaphors of growth and connection.

## Architecture

### Component Structure
```
CourseShowcasePage
├── Header (Navigation + Hero Section)
├── TeacherIntroduction
├── CoursePositioning  
├── CourseOutline (Interactive Audio Player)
├── CallToAction
└── Footer
```

### Audio System Architecture
```
AudioManager (Context Provider)
├── AudioPlayer (Persistent Player Component)
├── AudioControls (Play/Pause/Progress/Volume)
├── PlaylistManager (Lesson Navigation)
└── AudioState (Current Track/Progress/Loading States)
```

### State Management
- React Context for audio state management
- Local state for UI interactions (hover, focus, expanded sections)
- Audio Web API for media control and progress tracking

## Components and Interfaces

### 1. Header Component
**Visual Design:**
- Clean navigation bar with "遇见之屿" branding
- Hero section featuring course title with warm gradient overlay
- Subtle organic shapes in background using brand colors
- Trust blue (#6A8DAF) for navigation, warm sand (#F0EBE3) background

**Layout:**
- Desktop: Horizontal navigation, centered hero content
- Mobile: Hamburger menu, stacked hero elements
- Typography: "霞鹜文楷" for Chinese title (32-40px), "Nunito" for English elements

### 2. TeacherIntroduction Component
**Visual Design:**
- Card-based layout with soft shadows (0 2px 8px rgba(0,0,0,0.08))
- Professional photo placeholder with rounded corners (12px)
- Achievement highlights using trust blue (#6A8DAF) for credibility
- Warm background with white content areas

**Content Structure:**
- Personal introduction with emphasis on 20-year experience
- Key statistics: 5,000+ families, 600 consultations, 50%+ renewal rate
- Transformation story highlighting AI-assisted approach
- Call-out boxes for impressive numbers using action orange (#F7B787)

### 3. CoursePositioning Component
**Visual Design:**
- Two-column layout for target audiences
- Growth green (#A3B899) accent for course-related content
- Icon-based visual hierarchy for core selling points
- Expandable cards for detailed explanations

**Content Organization:**
- Target audience cards with clear personas
- Core selling points with visual emphasis on "经验迁移 + AI提效 + 理论补强"
- Benefit statements using emotional language
- Visual separation between practical vs. theoretical approaches

### 4. CourseOutline Component (Primary Interactive Element)
**Visual Design:**
- Accordion-style expandable sections for each course module
- Audio play buttons integrated into each lesson item
- Visual indicators for currently playing content (action orange #F7B787)
- Progress indicators for completed/current lessons

**Interactive Elements:**
- Clickable lesson items that trigger audio playback
- Hover states with subtle color transitions (0.3s ease)
- Active states showing current lesson with orange highlighting
- Expandable/collapsible module sections

**Audio Integration:**
- Play/pause icons next to each lesson
- Mini audio player that appears when content is selected
- Progress bars for individual lessons
- Seamless transition between lessons

### 5. AudioPlayer Component (Persistent)
**Visual Design:**
- Fixed position player at bottom of screen (mobile) or sidebar (desktop)
- Minimalist design with essential controls only
- Brand-consistent styling with rounded corners and soft shadows
- Smooth animations for show/hide states

**Functionality:**
- Standard audio controls (play/pause, progress, volume)
- Lesson title and module information display
- Skip to next/previous lesson functionality
- Responsive design adapting to screen size

## Data Models

### Course Structure
```typescript
interface CourseModule {
  id: string;
  title: string;
  description: string;
  color: 'growth' | 'trust' | 'action'; // Maps to brand colors
  lessons: Lesson[];
}

interface Lesson {
  id: string;
  title: string;
  duration: number; // in seconds
  audioUrl: string;
  description?: string;
  isCompleted: boolean;
}

interface TeacherProfile {
  name: string;
  age: number;
  experience: string;
  achievements: Achievement[];
  story: string;
  photoUrl: string;
}

interface Achievement {
  metric: string;
  value: string;
  description: string;
}
```

### Audio State
```typescript
interface AudioState {
  currentLesson: Lesson | null;
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  isLoading: boolean;
  playlist: Lesson[];
  currentIndex: number;
}
```

## Error Handling

### Audio Playback Errors
- **Network Issues:** Display retry button with gentle error message
- **Unsupported Format:** Fallback to alternative audio format or download link
- **Loading Failures:** Show loading spinner with timeout fallback
- **Playback Interruption:** Auto-resume functionality with user notification

### Responsive Design Failures
- **Layout Breakpoints:** Graceful degradation for unsupported screen sizes
- **Touch Interaction:** Fallback to click events for older devices
- **Font Loading:** System font fallbacks maintaining readability

### Accessibility Errors
- **Screen Reader Issues:** Comprehensive ARIA labels and semantic HTML
- **Keyboard Navigation:** Full keyboard accessibility with visible focus indicators
- **Color Contrast:** Ensure all text meets WCAG AA standards

## Testing Strategy

### Unit Testing
- Component rendering with correct props
- Audio state management functions
- Responsive breakpoint behavior
- Accessibility compliance (aria-labels, keyboard navigation)

### Integration Testing
- Audio playback functionality across different browsers
- Responsive design on various device sizes
- User interaction flows (lesson selection, audio control)
- Cross-browser compatibility (Chrome, Firefox, Safari, Edge)

### User Experience Testing
- Audio loading performance on different connection speeds
- Mobile touch interaction responsiveness
- Visual hierarchy and content comprehension
- Brand consistency across all components

### Performance Testing
- Audio file loading optimization
- Image optimization and lazy loading
- JavaScript bundle size optimization
- Core Web Vitals compliance (LCP, FID, CLS)

## Technical Specifications

### Audio Implementation
- **Format Support:** MP3 primary, OGG fallback
- **Streaming:** Progressive download with buffering indicators
- **Caching:** Browser cache optimization for repeated playback
- **Controls:** Custom HTML5 audio controls matching brand design

### Responsive Breakpoints
- **Mobile:** 320px - 767px (single column, stacked navigation)
- **Tablet:** 768px - 1023px (two-column layout, condensed spacing)
- **Desktop:** 1024px+ (full layout with sidebar audio player)

### Performance Optimization
- **Lazy Loading:** Images and non-critical content below fold
- **Code Splitting:** Separate bundles for audio functionality
- **Compression:** Gzip/Brotli compression for all assets
- **CDN:** Audio files served from optimized content delivery network

### Browser Support
- **Modern Browsers:** Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **Progressive Enhancement:** Core functionality works without JavaScript
- **Polyfills:** Web Audio API fallbacks for older browsers