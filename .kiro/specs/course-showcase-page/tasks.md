# Implementation Plan

- [x] 1. Set up project structure and core dependencies
  - Create React project with TypeScript and Vite configuration
  - Install required dependencies: React, styled-components, react-router-dom
  - Install audio libraries: react-use for audio hooks, web-audio-api types
  - Set up project folder structure: src/components, src/hooks, src/types, src/data
  - Configure TypeScript with strict mode and proper path aliases
  - _Requirements: 1.1, 2.1, 3.1, 4.1, 5.1, 6.1_

- [x] 2. Create design system and brand tokens
  - Create design tokens file with WebUI brand colors (#F0EBE3, #A3B899, #6A8DAF, #F7B787)
  - Set up typography system with "霞鹜文楷" and "Nunito" font loading
  - Implement responsive breakpoints (768px, 1024px) and spacing scale
  - Create styled-components theme provider with brand tokens
  - Build base UI components: Button, Card, Typography, Container
  - _Requirements: 1.3, 1.4, 2.3, 2.4, 5.2, 5.4_

- [x] 3. Implement course data structure and types
  - Create TypeScript interfaces for CourseModule, Lesson, TeacherProfile, Achievement
  - Build course data based on IP.md content with 5 modules (理论篇, 心法篇, 技法篇, AI篇, 商业篇)
  - Add mock audio URLs and lesson durations for each course section
  - Create teacher profile data for Stone Yuan (40岁, 20年教育经验, 5000+家庭服务)
  - Include key achievements: 90天600份咨询, 80%年轻客户, 50%+续费率
  - Set up course positioning data with target audiences and selling points
  - _Requirements: 1.1, 1.2, 2.1, 2.2, 3.1, 3.2, 3.3, 3.4_

- [x] 4. Build audio management system
- [x] 4.1 Create audio context and state management
  - Implement AudioContext provider using React Context API
  - Create AudioState interface with currentLesson, isPlaying, progress, volume
  - Build audio reducer for state management (play, pause, seek, next, previous)
  - Add audio loading states and error handling utilities
  - _Requirements: 4.1, 4.2, 4.4_

- [x] 4.2 Implement persistent audio player component
  - Create AudioPlayer component with HTML5 audio element
  - Build custom controls: play/pause button, progress bar, volume slider
  - Implement responsive positioning (fixed bottom on mobile, sidebar on desktop)
  - Add smooth show/hide animations with 0.3s transitions
  - Style with brand colors and ensure touch-friendly controls (44x44px minimum)
  - _Requirements: 4.2, 4.4, 5.3, 5.4_

- [x] 4.3 Add playlist and navigation functionality
  - Implement lesson switching with audio state synchronization
  - Create next/previous lesson navigation with playlist management
  - Add keyboard shortcuts for audio control (spacebar, arrow keys)
  - Build visual indicators for currently playing lesson using action orange (#F7B787)
  - _Requirements: 4.1, 4.5, 6.2, 6.3_

- [x] 5. Create page layout and header
  - Build responsive Header component with "遇见之屿" branding
  - Implement navigation menu (horizontal on desktop, hamburger on mobile)
  - Create hero section with course title "教育人 7天咨询起盘营"
  - Add organic background shapes using brand colors and gradients
  - Ensure proper semantic HTML structure and accessibility
  - _Requirements: 1.3, 5.2, 5.4, 6.1, 6.2_

- [x] 6. Implement teacher introduction section
  - Create TeacherIntroduction component with card-based layout
  - Display Stone Yuan's profile: 40岁, 20年教育经验, 5000+家庭服务
  - Highlight key achievements: 90天600份咨询, 80%年轻客户, 50%+续费率
  - Use trust blue (#6A8DAF) for professional content, action orange (#F7B787) for statistics
  - Add responsive image placeholder with proper alt text
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 6.4_

- [x] 7. Build course positioning section
  - Create CoursePositioning component with two-column responsive layout
  - Display target audiences: 教育/管理转型人群, 心理咨询证书新人
  - Highlight core selling points: "经验迁移 + AI提效 + 理论补强"
  - Use growth green (#A3B899) for course content and expandable cards
  - Add smooth expand/collapse animations for detailed explanations
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 5.2_

- [x] 8. Create interactive course outline
- [x] 8.1 Build course module structure
  - Create CourseOutline component with accordion-style expandable sections
  - Implement 5 course modules: 理论篇, 心法篇, 技法篇, AI篇, 商业篇
  - Add all lessons from IP.md with proper hierarchy and descriptions
  - Use appropriate brand color coding for different modules
  - _Requirements: 3.1, 3.2, 3.3, 3.4_

- [x] 8.2 Integrate audio playback with lessons
  - Make lesson items clickable to trigger audio playback
  - Add play/pause icons next to each lesson title
  - Implement visual indicators for currently playing content
  - Create hover states with smooth color transitions (0.3s ease)
  - Add loading states and progress indicators for audio content
  - _Requirements: 4.1, 4.3, 4.4, 4.5_

- [x] 8.3 Add lesson interaction and navigation
  - Connect lesson selection with audio player state
  - Create visual feedback for active and completed lessons
  - Ensure touch-friendly interaction targets (minimum 44x44px)
  - Add proper ARIA labels for screen reader accessibility
  - _Requirements: 4.1, 4.3, 4.5, 5.3, 6.2, 6.3_

- [x] 9. Implement responsive design and mobile optimization
  - Apply mobile-first responsive design across all components
  - Implement vertical navigation stacking for mobile screens (<768px)
  - Optimize audio controls for touch devices with larger tap targets
  - Test layout at tablet breakpoint (768px-1023px) and desktop (1024px+)
  - Add proper viewport meta tags and responsive font scaling
  - _Requirements: 5.1, 5.2, 5.3, 5.4, 5.5_

- [x] 10. Add accessibility features and WCAG compliance
  - Implement comprehensive ARIA labels for all interactive elements
  - Create keyboard navigation support with visible focus indicators (2px #6A8DAF outline)
  - Add screen reader announcements for audio state changes
  - Ensure color contrast compliance for all text elements
  - Add skip links and proper heading hierarchy
  - _Requirements: 6.1, 6.2, 6.3, 6.4, 6.5_

- [x] 11. Performance optimization and error handling
  - Implement lazy loading for images and non-critical content
  - Add error boundaries for graceful error handling
  - Optimize audio file loading with progressive download
  - Implement code splitting for audio functionality
  - Add loading states and retry mechanisms for failed audio loads
  - _Requirements: 4.2, 4.4, 5.1, 6.1_

- [x] 12. Final integration and testing
  - Integrate all components into main CourseShowcasePage
  - Add call-to-action section with enrollment functionality
  - Implement smooth page transitions and micro-interactions
  - Perform cross-browser testing (Chrome, Firefox, Safari, Edge)
  - Test responsive design across all breakpoints and devices
  - _Requirements: 1.1, 2.1, 3.1, 4.1, 5.1, 6.1_