# Requirements Document

## Introduction

This feature creates a comprehensive course showcase page for "教育人 7天咨询起盘营" (7-Day Consulting Startup Camp for Educators) on the "遇见之屿" platform. The page will present teacher <PERSON>'s background, course positioning, and detailed curriculum in an engaging, interactive format that allows users to access audio content for each lesson section.

## Requirements

### Requirement 1

**User Story:** As a potential student, I want to see the teacher's professional background and credentials, so that I can trust their expertise and decide whether to enroll in the course.

#### Acceptance Criteria

1. WHEN a user visits the course page THEN the system SHALL display <PERSON>'s introduction including her 20-year education experience, 5,000+ families served, and 2025 transformation to psychological consulting
2. WHEN displaying teacher credentials THEN the system SHALL highlight key achievements: 600 consultations in 90 days, 80% clients aged 18-30, 50%+ renewal rate
3. WHEN presenting the teacher section THEN the system SHALL use the brand's warm sand background (#F0EBE3) with trust blue (#6A8DAF) for professional content
4. WHEN showing teacher information THEN the system SHALL use "霞鹜文楷" font for Chinese text to convey warmth and care

### Requirement 2

**User Story:** As a prospective learner, I want to understand the course positioning and target audience, so that I can determine if this course matches my needs and background.

#### Acceptance Criteria

1. WHEN a user views course positioning THEN the system SHALL clearly display the two target groups: professionals with education/management experience wanting to transition to psychological consulting, and certified counselors lacking practical experience
2. WHEN presenting core selling points THEN the system SHALL emphasize "experience migration + AI efficiency + theory reinforcement" approach
3. WHEN displaying positioning content THEN the system SHALL use growth green (#A3B899) for course-related content and sections
4. WHEN showing target audience THEN the system SHALL use card components with soft shadows (0 2px 8px rgba(0,0,0,0.08)) and rounded corners (8-12px)

### Requirement 3

**User Story:** As an interested student, I want to see the detailed course outline with all modules and lessons, so that I can understand what I will learn and the course structure.

#### Acceptance Criteria

1. WHEN a user accesses the course outline THEN the system SHALL display all five main sections: Theory, Mindset, Techniques, AI, and Business
2. WHEN showing each section THEN the system SHALL list all subsections and topics in a hierarchical, expandable format
3. WHEN displaying the curriculum THEN the system SHALL use appropriate brand colors: growth green (#A3B899) for course content, trust blue (#6A8DAF) for section headers
4. WHEN presenting course structure THEN the system SHALL organize content in a visually clear, scannable format with proper spacing and typography hierarchy

### Requirement 4

**User Story:** As a course participant, I want to click on any lesson in the outline to play its audio content, so that I can immediately access and listen to the course materials.

#### Acceptance Criteria

1. WHEN a user clicks on any lesson item THEN the system SHALL trigger audio playback for that specific lesson
2. WHEN audio is playing THEN the system SHALL provide standard audio controls (play/pause, progress bar, volume control)
3. WHEN a lesson is selected THEN the system SHALL visually indicate the currently active lesson using action orange (#F7B787)
4. WHEN audio playback starts THEN the system SHALL show a persistent audio player that doesn't interfere with page navigation
5. WHEN switching between lessons THEN the system SHALL stop the current audio and start the new selection

### Requirement 5

**User Story:** As a mobile user, I want the course page to work seamlessly on my phone or tablet, so that I can browse and listen to course content on any device.

#### Acceptance Criteria

1. WHEN accessing the page on mobile devices THEN the system SHALL use responsive design with mobile-first approach
2. WHEN viewing on screens smaller than 768px THEN the system SHALL stack navigation vertically and adjust layout accordingly
3. WHEN interacting on touch devices THEN the system SHALL ensure all touch targets are minimum 44x44px
4. WHEN displaying on different screen sizes THEN the system SHALL maintain proper spacing: 16px on mobile, 24px on tablet, 32px on desktop
5. WHEN using the audio player on mobile THEN the system SHALL provide touch-friendly controls optimized for mobile interaction

### Requirement 6

**User Story:** As a user with accessibility needs, I want the page to be accessible with screen readers and keyboard navigation, so that I can fully access the course content regardless of my abilities.

#### Acceptance Criteria

1. WHEN using keyboard navigation THEN the system SHALL provide visible focus indicators with 2px #6A8DAF outline
2. WHEN accessing with screen readers THEN the system SHALL include proper ARIA labels and semantic HTML structure
3. WHEN interacting with audio controls THEN the system SHALL support keyboard shortcuts and screen reader announcements
4. WHEN viewing content THEN the system SHALL maintain sufficient color contrast ratios for all text elements
5. WHEN navigating the course outline THEN the system SHALL provide logical tab order and skip links where appropriate