# 设计文档

## 概述

内容管理系统将为遇见之屿课程展示页面提供配置化的内容管理能力。系统采用前后端分离架构，前端继续使用React + TypeScript + Vite技术栈，后端使用Node.js + Express构建简单的管理API，通过JSON配置文件存储数据。

## 架构

### 整体架构图

```mermaid
graph TB
    subgraph "前端展示层"
        A[课程展示页面] --> B[配置读取模块]
        B --> C[本地配置文件]
    end
    
    subgraph "后端管理层"
        D[管理界面] --> E[Express API]
        E --> F[配置文件操作]
        F --> C
    end
    
    subgraph "配置存储层"
        C --> G[系统配置文件<br/>system-config.json]
        C --> H[课程配置文件<br/>course-config.json]
    end
    
    I[开发者手动编辑] --> C
```

### 技术架构

- **前端展示**: React 19.1.0 + TypeScript + Styled Components
- **后端管理**: Node.js + Express + TypeScript
- **数据存储**: JSON配置文件
- **构建工具**: Vite (前端) + tsc (后端)
- **开发工具**: 共享ESLint配置

## 组件和接口

### 前端组件架构

#### 1. 配置管理组件
```typescript
// 配置提供者组件
interface ConfigProviderProps {
  children: React.ReactNode;
}

// 配置上下文
interface ConfigContextType {
  systemConfig: SystemConfig;
  courseConfig: CourseConfig;
  refreshConfig: () => Promise<void>;
  isLoading: boolean;
  error: string | null;
}
```

#### 2. 配置读取Hook
```typescript
// 自定义Hook用于读取配置
interface UseConfigReturn {
  systemConfig: SystemConfig;
  courseConfig: CourseConfig;
  isLoading: boolean;
  error: string | null;
  refreshConfig: () => Promise<void>;
}
```

### 后端API接口

#### 1. 认证接口
```typescript
// POST /api/auth/login
interface LoginRequest {
  password: string;
}

interface LoginResponse {
  success: boolean;
  token?: string;
  message: string;
}
```

#### 2. 系统配置接口
```typescript
// GET /api/config/system
interface SystemConfigResponse {
  success: boolean;
  data: SystemConfig;
}

// PUT /api/config/system
interface UpdateSystemConfigRequest {
  config: SystemConfig;
}
```

#### 3. 课程配置接口
```typescript
// GET /api/config/course
interface CourseConfigResponse {
  success: boolean;
  data: CourseConfig;
}

// PUT /api/config/course
interface UpdateCourseConfigRequest {
  config: CourseConfig;
}
```

### 管理界面组件

#### 1. 登录组件
```typescript
interface LoginFormProps {
  onLogin: (password: string) => Promise<void>;
  isLoading: boolean;
  error: string | null;
}
```

#### 2. 系统配置管理组件
```typescript
interface SystemConfigEditorProps {
  config: SystemConfig;
  onSave: (config: SystemConfig) => Promise<void>;
  isLoading: boolean;
}
```

#### 3. 课程配置管理组件
```typescript
interface CourseConfigEditorProps {
  config: CourseConfig;
  onSave: (config: CourseConfig) => Promise<void>;
  isLoading: boolean;
}
```

## 数据模型

### 系统配置数据模型
```typescript
interface SystemConfig {
  // 页面基本信息
  pageInfo: {
    title: string;
    subtitle: string;
    description: string;
  };
  
  // 导航文本
  navigation: {
    home: string;
    about: string;
    courses: string;
    contact: string;
  };
  
  // 按钮文本
  buttons: {
    playAudio: string;
    pauseAudio: string;
    enrollNow: string;
    learnMore: string;
    backToTop: string;
  };
  
  // 表单文本
  forms: {
    contactForm: {
      nameLabel: string;
      emailLabel: string;
      messageLabel: string;
      submitButton: string;
      successMessage: string;
      errorMessage: string;
    };
  };
  
  // 页脚文本
  footer: {
    copyright: string;
    contactInfo: string;
    socialLinks: {
      wechat: string;
      weibo: string;
      email: string;
    };
  };
  
  // 无障碍文本
  accessibility: {
    skipToContent: string;
    audioPlayerControls: string;
    courseOutlineNavigation: string;
  };
}
```

### 课程配置数据模型
```typescript
interface CourseConfig {
  // 课程基本信息
  courseInfo: {
    title: string;
    subtitle: string;
    description: string;
    duration: string;
    level: string;
    price: string;
  };
  
  // 讲师信息
  teacher: {
    name: string;
    title: string;
    bio: string;
    experience: string;
    photoUrl: string;
    achievements: Array<{
      metric: string;
      value: string;
      description: string;
    }>;
  };
  
  // 课程模块
  modules: Array<{
    id: string;
    title: string;
    description: string;
    color: 'growth' | 'trust' | 'action';
    lessons: Array<{
      id: string;
      title: string;
      description: string;
      duration: number; // 秒
      audioUrl: string;
      isCompleted: boolean;
    }>;
  }>;
  
  // 课程定位
  positioning: {
    targetAudiences: Array<{
      title: string;
      description: string;
    }>;
    coreSellingPoints: string[];
    uniqueValue: string;
  };
}
```

## 错误处理

### 前端错误处理策略
1. **配置加载失败**: 使用默认配置，显示警告提示
2. **网络请求失败**: 重试机制，最多3次
3. **配置格式错误**: 显示具体错误信息，提供修复建议
4. **权限验证失败**: 重定向到登录页面

### 后端错误处理策略
1. **文件读取失败**: 返回默认配置
2. **文件写入失败**: 返回详细错误信息
3. **JSON解析失败**: 返回格式错误提示
4. **权限验证失败**: 返回401状态码

### 错误响应格式
```typescript
interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
}
```

## 测试策略

### 单元测试
- **配置读取Hook测试**: 测试配置加载、错误处理、重试机制
- **API接口测试**: 测试所有CRUD操作
- **组件渲染测试**: 测试管理界面组件的正确渲染

### 集成测试
- **配置文件操作测试**: 测试文件读写的完整流程
- **前后端集成测试**: 测试API调用和数据同步
- **错误场景测试**: 测试各种异常情况的处理

### 端到端测试
- **管理流程测试**: 测试完整的登录-编辑-保存流程
- **配置更新测试**: 测试配置更新后前端页面的实时更新
- **手动编辑测试**: 测试手动修改配置文件后的系统响应

## 性能考虑

### 前端性能优化
1. **配置缓存**: 使用localStorage缓存配置，减少重复请求
2. **懒加载**: 管理界面组件按需加载
3. **防抖处理**: 配置保存操作使用防抖，避免频繁写入
4. **错误边界**: 使用ErrorBoundary包装配置相关组件

### 后端性能优化
1. **文件缓存**: 内存中缓存配置文件内容
2. **文件监听**: 使用fs.watch监听配置文件变化
3. **请求限制**: 实现简单的请求频率限制
4. **响应压缩**: 使用gzip压缩API响应

### 配置文件优化
1. **文件大小控制**: 限制配置文件大小，避免过大影响性能
2. **格式验证**: 使用JSON Schema验证配置格式
3. **备份机制**: 自动备份配置文件，防止数据丢失

## 安全考虑

### 认证安全
1. **密码验证**: 使用bcrypt对密码进行哈希验证
2. **会话管理**: 使用JWT token进行会话管理
3. **超时机制**: 设置合理的会话超时时间

### 文件安全
1. **路径验证**: 严格验证文件路径，防止路径遍历攻击
2. **权限控制**: 限制配置文件的读写权限
3. **输入验证**: 对所有输入进行严格验证和清理

### API安全
1. **CORS配置**: 合理配置跨域访问策略
2. **请求验证**: 验证所有API请求的格式和内容
3. **错误信息**: 避免在错误响应中泄露敏感信息

## 部署考虑

### 开发环境
- 前端和后端同时启动，支持热重载
- 配置文件变化自动同步到前端
- 详细的错误日志和调试信息

### 生产环境
- 前端构建为静态文件
- 后端作为独立服务运行
- 配置文件权限严格控制
- 日志记录和监控