# 技术栈与构建系统

## 核心技术栈
- **前端框架**: React 19.1.0 + TypeScript 5.8.3
- **构建工具**: Vite 7.0.4
- **样式方案**: Styled Components 6.1.19
- **路由**: React Router DOM 7.7.1
- **工具库**: React Use 17.6.0

## 开发工具
- **代码检查**: ESLint 9.30.1 + TypeScript ESLint
- **类型检查**: TypeScript (严格模式)
- **包管理**: npm

## 项目配置
- **模块系统**: ES Modules
- **目标环境**: 现代浏览器
- **开发服务器**: Vite Dev Server (HMR支持)

## 路径别名配置
```typescript
'@': './src'
'@/components': './src/components'
'@/hooks': './src/hooks'
'@/types': './src/types'
'@/data': './src/data'
```

## 常用命令

### 开发环境
```bash
cd course-showcase-page
npm run dev          # 启动开发服务器
npm run lint         # 运行代码检查
```

### 构建部署
```bash
npm run build        # 构建生产版本 (TypeScript编译 + Vite构建)
npm run preview      # 预览构建结果
```

### 代码质量
```bash
npm run lint         # ESLint代码检查
tsc --noEmit        # TypeScript类型检查
```

## 性能优化特性
- 组件懒加载 (React.lazy)
- 图片懒加载
- 音频懒加载
- 性能监控工具
- 跨浏览器兼容性检测

## 无障碍访问
- ARIA标签支持
- 键盘导航
- 屏幕阅读器支持
- 颜色对比度检查
- 触控目标最小44px