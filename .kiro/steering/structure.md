# 项目结构与架构模式

## 项目组织结构
```
course-showcase-page/
├── src/
│   ├── components/          # React组件
│   │   ├── ui/             # 基础UI组件 (Button, Card, Container等)
│   │   ├── *.tsx           # 业务组件
│   │   └── index.ts        # 组件统一导出
│   ├── hooks/              # 自定义React Hooks
│   ├── theme/              # 设计系统与主题
│   │   ├── theme.ts        # 主题配置
│   │   ├── tokens.ts       # 设计令牌
│   │   ├── GlobalStyles.tsx # 全局样式
│   │   └── ThemeProvider.tsx # 主题提供者
│   ├── types/              # TypeScript类型定义
│   ├── data/               # 静态数据与配置
│   ├── utils/              # 工具函数
│   ├── App.tsx             # 应用根组件
│   └── main.tsx            # 应用入口
├── public/                 # 静态资源
└── dist/                   # 构建输出
```

## 架构模式

### 组件架构
- **原子设计模式**: UI组件按原子(Button)、分子(Card)、组织(Header)层级组织
- **容器/展示组件分离**: 业务逻辑与UI展示分离
- **组件组合**: 通过组合而非继承构建复杂组件

### 状态管理
- **本地状态**: 使用React useState/useReducer
- **全局状态**: Context API (AudioProvider, ThemeProvider)
- **服务器状态**: 暂未使用外部状态管理库

### 样式架构
- **设计系统**: 基于设计令牌的主题系统
- **Styled Components**: CSS-in-JS解决方案
- **响应式设计**: 移动优先，断点式布局

## 代码组织规范

### 文件命名
- **组件文件**: PascalCase (如 `CourseShowcasePage.tsx`)
- **Hook文件**: camelCase with use前缀 (如 `useAudio.tsx`)
- **工具文件**: camelCase (如 `audioUtils.ts`)
- **类型文件**: camelCase (如 `index.ts`)

### 导入导出
- **统一导出**: 使用 `index.ts` 文件统一导出模块
- **命名导出**: 优先使用命名导出而非默认导出
- **路径别名**: 使用 `@/` 别名简化导入路径

### 组件结构
```typescript
// 1. 导入依赖
import React from 'react';
import styled from 'styled-components';

// 2. 类型定义
interface ComponentProps {
  // props定义
}

// 3. 样式组件
const StyledComponent = styled.div`
  // 样式定义
`;

// 4. 主组件
export const Component: React.FC<ComponentProps> = ({ ...props }) => {
  // 组件逻辑
  return (
    <StyledComponent>
      {/* JSX */}
    </StyledComponent>
  );
};
```

## 设计系统集成

### 主题令牌使用
- 颜色: `theme.colors.trustBlue`
- 间距: `theme.spacing[4]`
- 字体: `theme.typography.fonts.chinese`
- 断点: `theme.mediaQueries.tablet`

### 品牌色彩应用
- **主背景**: `#F0EBE3` (暖沙色) - 60%占比
- **功能色**: `#A3B899` (治愈绿) + `#6A8DAF` (信赖蓝) - 30%占比  
- **强调色**: `#F7B787` (暖阳橙) - 10%占比
- **文字色**: `#333333` (深炭灰)

## 性能优化策略
- **代码分割**: React.lazy + Suspense
- **资源懒加载**: 图片、音频按需加载
- **错误边界**: ErrorBoundary组件包装
- **性能监控**: 内置性能监控工具