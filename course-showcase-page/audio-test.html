<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音频测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .audio-test {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }

        .audio-test h3 {
            margin-top: 0;
            color: #333;
        }

        audio {
            width: 100%;
            margin: 10px 0;
        }

        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }

        .success {
            background-color: #d4edda;
            color: #155724;
        }

        .error {
            background-color: #f8d7da;
            color: #721c24;
        }

        .info {
            background-color: #d1ecf1;
            color: #0c5460;
        }

        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }

        button:hover {
            background: #0056b3;
        }

        .test-results {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
    </style>
</head>

<body>
    <h1>🎵 音频文件测试</h1>
    <p>这个页面用于测试音频文件是否可以正常播放</p>

    <div id="audio-tests"></div>

    <div class="test-results">
        <h3>测试结果</h3>
        <div id="results"></div>
    </div>

    <script>
        const audioFiles = [
            { file: '01.mp3', title: '心理学的起源与发展', module: '理论篇' },
            { file: '02.mp3', title: '心理学主要流派简介', module: '理论篇' },
            { file: '03.mp3', title: '常见心理问题与特点速览', module: '理论篇' },
            { file: '04.mp3', title: '导论：你的过去，是最好的起点', module: '心法篇' },
            { file: '05.mp3', title: '重新定义"专业"：AI时代的咨询师新模型', module: '心法篇' }
        ];

        const testResults = [];
        const testsContainer = document.getElementById('audio-tests');
        const resultsContainer = document.getElementById('results');

        function createAudioTest(audioFile) {
            const testDiv = document.createElement('div');
            testDiv.className = 'audio-test';
            testDiv.innerHTML = `
                <h3>${audioFile.file} - ${audioFile.title}</h3>
                <p><strong>模块:</strong> ${audioFile.module}</p>
                <audio controls preload="metadata">
                    <source src="/audio/${audioFile.file}" type="audio/mpeg">
                    您的浏览器不支持音频播放。
                </audio>
                <div class="status info" id="status-${audioFile.file}">
                    准备测试...
                </div>
                <button onclick="testAudio('${audioFile.file}')">测试播放</button>
                <button onclick="checkFile('${audioFile.file}')">检查文件</button>
            `;
            return testDiv;
        }

        function updateStatus(file, message, type = 'info') {
            const statusDiv = document.getElementById(`status-${file}`);
            if (statusDiv) {
                statusDiv.textContent = message;
                statusDiv.className = `status ${type}`;
            }
        }

        async function checkFile(file) {
            updateStatus(file, '正在检查文件...', 'info');

            try {
                const response = await fetch(`/audio/${file}`, { method: 'HEAD' });
                if (response.ok) {
                    const contentLength = response.headers.get('content-length');
                    const contentType = response.headers.get('content-type');
                    updateStatus(file, `文件存在 - 大小: ${contentLength ? Math.round(contentLength / 1024 / 1024 * 100) / 100 + 'MB' : '未知'}, 类型: ${contentType || '未知'}`, 'success');
                    testResults.push({ file, status: 'file_exists', details: { size: contentLength, type: contentType } });
                } else {
                    updateStatus(file, `文件不存在 (HTTP ${response.status})`, 'error');
                    testResults.push({ file, status: 'file_missing', details: { httpStatus: response.status } });
                }
            } catch (error) {
                updateStatus(file, `检查失败: ${error.message}`, 'error');
                testResults.push({ file, status: 'check_error', details: { error: error.message } });
            }

            updateResults();
        }

        function testAudio(file) {
            updateStatus(file, '正在测试播放...', 'info');

            const audio = new Audio(`/audio/${file}`);

            audio.addEventListener('loadstart', () => {
                updateStatus(file, '开始加载音频...', 'info');
            });

            audio.addEventListener('canplay', () => {
                updateStatus(file, '音频可以播放', 'success');
                testResults.push({ file, status: 'can_play', details: { duration: audio.duration } });
                updateResults();
            });

            audio.addEventListener('error', (e) => {
                const errorMsg = `播放错误: ${audio.error?.message || '未知错误'}`;
                updateStatus(file, errorMsg, 'error');
                testResults.push({ file, status: 'play_error', details: { error: audio.error } });
                updateResults();
            });

            audio.addEventListener('loadedmetadata', () => {
                updateStatus(file, `音频信息加载完成 - 时长: ${Math.round(audio.duration)}秒`, 'success');
            });

            // 尝试播放
            audio.play().then(() => {
                updateStatus(file, '音频播放成功！', 'success');
                // 播放2秒后暂停
                setTimeout(() => {
                    audio.pause();
                    updateStatus(file, '测试完成 - 音频播放正常', 'success');
                }, 2000);
            }).catch((error) => {
                updateStatus(file, `播放失败: ${error.message}`, 'error');
                testResults.push({ file, status: 'play_failed', details: { error: error.message } });
                updateResults();
            });
        }

        function updateResults() {
            const summary = testResults.reduce((acc, result) => {
                acc[result.status] = (acc[result.status] || 0) + 1;
                return acc;
            }, {});

            resultsContainer.innerHTML = `
                <h4>测试统计</h4>
                <ul>
                    ${Object.entries(summary).map(([status, count]) =>
                `<li><strong>${status}:</strong> ${count}个文件</li>`
            ).join('')}
                </ul>
                <h4>详细结果</h4>
                <pre>${JSON.stringify(testResults, null, 2)}</pre>
            `;
        }

        // 初始化测试界面
        audioFiles.forEach(audioFile => {
            testsContainer.appendChild(createAudioTest(audioFile));
        });

        // 自动检查所有文件
        window.addEventListener('load', () => {
            audioFiles.forEach(audioFile => {
                setTimeout(() => checkFile(audioFile.file), 500);
            });
        });
    </script>
</body>

</html>