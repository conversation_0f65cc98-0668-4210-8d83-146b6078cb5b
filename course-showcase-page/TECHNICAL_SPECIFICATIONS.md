# 技术规格说明书

## 系统架构

### 前端技术栈
```json
{
  "framework": "React 19.1.0",
  "language": "TypeScript 5.8.3",
  "bundler": "Vite 7.0.4",
  "styling": "Styled Components 6.1.19",
  "routing": "React Router DOM 7.7.1",
  "utilities": "React Use 17.6.0"
}
```

### 核心组件架构

#### 1. 音频播放系统
```typescript
// 核心Hook
useSimpleAudio() {
  state: SimpleAudioState
  playLesson: (lesson: Lesson) => void
  pauseAudio: () => void
  resumeAudio: () => void
  setVolume: (volume: number) => void
  setPlaybackRate: (rate: number) => void
  seekTo: (time: number) => void
  setOnAudioEnded: (callback: () => void) => void
}

// 状态接口
interface SimpleAudioState {
  currentLesson: Lesson | null
  isPlaying: boolean
  currentTime: number
  duration: number
  volume: number
  playbackRate: number
  error: string | null
}
```

#### 2. 主题系统
```typescript
// 设计令牌
const tokens = {
  colors: {
    warmSand: '#F0EBE3',      // 主背景色 60%
    healingGreen: '#A3B899',   // 治愈绿 30%
    trustBlue: '#6A8DAF',      // 信赖蓝 30%
    actionOrange: '#F7B787',   // 暖阳橙 10%
    textPrimary: '#333333'     // 主文字色
  },
  typography: {
    fonts: {
      chinese: '"霞鹜文楷", "Noto Serif SC", serif',
      english: '"Nunito", "Inter", sans-serif'
    }
  }
}
```

## API接口设计

### 音频文件映射
```typescript
// 音频文件命名规范
const audioMapping = {
  'theory-1': '/audio/01.mp3',
  'theory-2': '/audio/02.mp3',
  'theory-3': '/audio/03.mp3',
  'mindset-1': '/audio/04.mp3',
  'mindset-2': '/audio/05.mp3',
  'mindset-3': '/audio/06.mp3',
  'techniques-1': '/audio/07.mp3',
  'techniques-2': '/audio/08.mp3',
  'techniques-3': '/audio/09.mp3',
  'techniques-4': '/audio/10.mp3',
  'ai-1': '/audio/11.mp3',
  'ai-2': '/audio/12.mp3',
  'ai-3': '/audio/13.mp3',
  'business-1': '/audio/14.mp3',
  'business-2': '/audio/15.mp3',
  'business-3': '/audio/16.mp3'
}
```

### 课程数据结构
```typescript
interface CourseModule {
  id: string
  title: string
  description: string
  color: 'trust' | 'growth' | 'action'
  lessons: Lesson[]
}

interface Lesson {
  id: string
  title: string
  duration: number
  audioUrl: string
  description: string
  isCompleted: boolean
}
```

## 响应式断点

```scss
$breakpoints: (
  mobile: 0px,
  tablet: 768px,
  desktop: 1024px,
  wide: 1440px
);

// 媒体查询
@media (max-width: 767px) { /* 移动端 */ }
@media (min-width: 768px) and (max-width: 1023px) { /* 平板端 */ }
@media (min-width: 1024px) { /* 桌面端 */ }
```

## 性能优化策略

### 1. 代码分割
```typescript
// 组件懒加载
const LazyComponent = React.lazy(() => import('./Component'))

// 路由级别分割
const routes = [
  {
    path: '/',
    component: React.lazy(() => import('./pages/Home'))
  }
]
```

### 2. 资源优化
```typescript
// 图片懒加载
<LazyImage
  src="/images/teacher.jpg"
  alt="教师照片"
  loading="lazy"
  placeholder={<Skeleton />}
/>

// 音频预加载
audio.preload = 'metadata'
```

### 3. 状态管理优化
```typescript
// 使用useCallback稳定函数引用
const handlePlay = useCallback(() => {
  playLesson(currentLesson)
}, [currentLesson, playLesson])

// 使用useMemo缓存计算结果
const filteredLessons = useMemo(() => {
  return lessons.filter(lesson => lesson.audioUrl)
}, [lessons])
```

## 无障碍访问规范

### ARIA标签使用
```html
<!-- 播放器 -->
<div role="region" aria-label="音频播放器">
  <button aria-label="播放/暂停" aria-pressed="false">
  <div role="progressbar" aria-valuenow="30" aria-valuemin="0" aria-valuemax="100">
</div>

<!-- 课程列表 -->
<div role="list" aria-label="课程列表">
  <div role="listitem" aria-label="第1课：心理学起源">
</div>
```

### 键盘导航
```typescript
// 键盘事件处理
const handleKeyDown = (event: KeyboardEvent) => {
  switch (event.key) {
    case ' ': // 空格键播放/暂停
      event.preventDefault()
      togglePlay()
      break
    case 'ArrowLeft': // 左箭头后退
      seekTo(currentTime - 15)
      break
    case 'ArrowRight': // 右箭头前进
      seekTo(currentTime + 15)
      break
  }
}
```

## 错误处理机制

### 1. 音频加载错误
```typescript
const handleAudioError = (error: MediaError) => {
  const errorMessages = {
    1: '音频加载被中止',
    2: '网络错误导致音频加载失败',
    3: '音频解码失败',
    4: '不支持的音频格式'
  }
  
  setState(prev => ({
    ...prev,
    error: errorMessages[error.code] || '未知音频错误',
    isPlaying: false
  }))
}
```

### 2. 组件错误边界
```typescript
class ErrorBoundary extends React.Component {
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('组件错误:', error, errorInfo)
    
    // 错误上报
    if (process.env.NODE_ENV === 'production') {
      reportError(error, errorInfo)
    }
  }
}
```

## 构建配置

### Vite配置
```typescript
// vite.config.ts
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@/components': path.resolve(__dirname, './src/components'),
      '@/hooks': path.resolve(__dirname, './src/hooks'),
      '@/types': path.resolve(__dirname, './src/types'),
      '@/data': path.resolve(__dirname, './src/data')
    }
  },
  build: {
    target: 'es2015',
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          ui: ['styled-components']
        }
      }
    }
  }
})
```

### TypeScript配置
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  }
}
```

## 部署规范

### 1. 静态资源
```bash
# 音频文件放置
public/
├── audio/
│   ├── 01.mp3  # 理论篇第1课
│   ├── 02.mp3  # 理论篇第2课
│   └── ...     # 共16个音频文件
└── images/
    └── teacher-stone-yuan.jpg
```

### 2. 环境变量
```env
# .env.production
VITE_API_BASE_URL=https://api.example.com
VITE_AUDIO_BASE_URL=https://cdn.example.com/audio
VITE_ENABLE_ANALYTICS=true
```

### 3. 构建命令
```bash
# 开发环境
npm run dev

# 类型检查
npm run type-check

# 代码检查
npm run lint

# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

## 监控和分析

### 性能监控
```typescript
// 性能指标收集
const performanceObserver = new PerformanceObserver((list) => {
  list.getEntries().forEach((entry) => {
    if (entry.entryType === 'navigation') {
      console.log('页面加载时间:', entry.loadEventEnd - entry.loadEventStart)
    }
  })
})

performanceObserver.observe({ entryTypes: ['navigation', 'measure'] })
```

### 用户行为分析
```typescript
// 音频播放统计
const trackAudioPlay = (lessonId: string, duration: number) => {
  analytics.track('audio_play', {
    lesson_id: lessonId,
    duration: duration,
    timestamp: Date.now()
  })
}
```

---

**技术特色**: 
- 🚀 现代化技术栈，性能优异
- 🎨 完善的设计系统，视觉统一
- ♿ 全面的无障碍支持，包容性强
- 📱 完美的响应式设计，跨设备兼容
- 🔧 健壮的错误处理，用户体验佳