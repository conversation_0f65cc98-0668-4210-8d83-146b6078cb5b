#!/usr/bin/env node

/**
 * 音频文件检查脚本
 * 用于验证所有音频文件是否正确放置
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 音频文件映射
const audioMapping = [
  { file: '01.mp3', title: '心理学的起源与发展', module: '理论篇' },
  { file: '02.mp3', title: '心理学主要流派简介', module: '理论篇' },
  { file: '03.mp3', title: '常见心理问题与特点速览', module: '理论篇' },
  { file: '04.mp3', title: '导论：你的过去，是最好的起点', module: '心法篇' },
  { file: '05.mp3', title: '重新定义"专业"：AI时代的咨询师新模型', module: '心法篇' },
  { file: '06.mp3', title: '建立边界感：从"热心肠"到"专业助人者"', module: '心法篇' },
  { file: '07.mp3', title: '首次链接：如何30分钟建立信任感？', module: '技法篇' },
  { file: '08.mp3', title: '咨询过程：结构化咨询的"四步法"', module: '技法篇' },
  { file: '09.mp3', title: '提问的艺术：成为"问题"的专家', module: '技法篇' },
  { file: '10.mp3', title: '收尾总结：让来访者带着希望离开的结构', module: '技法篇' },
  { file: '11.mp3', title: '咨询前准备：AI帮你做案例预判和方案设计', module: 'AI篇' },
  { file: '12.mp3', title: '咨询中辅助：实时话术提醒和情绪识别', module: 'AI篇' },
  { file: '13.mp3', title: '咨询后复盘：AI协助案例分析和成长记录', module: 'AI篇' },
  { file: '14.mp3', title: '如何找到前100个练手机会', module: '商业篇' },
  { file: '15.mp3', title: '定价策略和服务边界设定', module: '商业篇' },
  { file: '16.mp3', title: '危机干预和转介绍流程', module: '商业篇' }
];

const audioDir = path.join(__dirname, 'public', 'audio');

console.log('🎵 检查音频文件...\n');

// 检查音频目录是否存在
if (!fs.existsSync(audioDir)) {
  console.log('❌ 音频目录不存在，正在创建...');
  fs.mkdirSync(audioDir, { recursive: true });
  console.log('✅ 音频目录已创建：public/audio/\n');
}

let missingFiles = [];
let existingFiles = [];

// 检查每个音频文件
audioMapping.forEach(({ file, title, module }) => {
  const filePath = path.join(audioDir, file);
  
  if (fs.existsSync(filePath)) {
    const stats = fs.statSync(filePath);
    const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
    existingFiles.push({ file, title, module, size: sizeInMB });
    console.log(`✅ ${file} - ${title} (${sizeInMB}MB)`);
  } else {
    missingFiles.push({ file, title, module });
    console.log(`❌ ${file} - ${title} (缺失)`);
  }
});

console.log('\n📊 检查结果：');
console.log(`✅ 已找到文件：${existingFiles.length}/${audioMapping.length}`);
console.log(`❌ 缺失文件：${missingFiles.length}/${audioMapping.length}`);

if (missingFiles.length > 0) {
  console.log('\n📋 缺失的音频文件：');
  missingFiles.forEach(({ file, title, module }) => {
    console.log(`   ${file} - ${module} - ${title}`);
  });
  
  console.log('\n💡 请将对应的音频文件重命名并复制到 public/audio/ 目录下');
}

if (existingFiles.length > 0) {
  console.log('\n📁 已存在的音频文件：');
  existingFiles.forEach(({ file, title, module, size }) => {
    console.log(`   ${file} - ${module} - ${title} (${size}MB)`);
  });
}

console.log('\n🚀 完成检查！');

if (missingFiles.length === 0) {
  console.log('🎉 所有音频文件都已正确放置，可以启动开发服务器测试播放功能！');
  console.log('   运行命令：npm run dev');
} else {
  console.log('⚠️  请先添加缺失的音频文件，然后重新运行此脚本检查。');
}