#!/usr/bin/env node

/**
 * 音频播放调试工具
 * 用于诊断音频播放问题
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔍 音频播放问题诊断工具\n');

// 1. 检查项目结构
console.log('📁 检查项目结构...');
const publicDir = path.join(__dirname, 'public');
const audioDir = path.join(publicDir, 'audio');

if (!fs.existsSync(publicDir)) {
    console.log('❌ public 目录不存在');
} else {
    console.log('✅ public 目录存在');
}

if (!fs.existsSync(audioDir)) {
    console.log('❌ public/audio 目录不存在');
} else {
    console.log('✅ public/audio 目录存在');
    
    // 列出音频文件
    const audioFiles = fs.readdirSync(audioDir).filter(file => file.endsWith('.mp3'));
    console.log(`📄 找到 ${audioFiles.length} 个音频文件:`);
    audioFiles.forEach(file => {
        const filePath = path.join(audioDir, file);
        const stats = fs.statSync(filePath);
        const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
        console.log(`   ${file} (${sizeInMB}MB)`);
    });
}

// 2. 检查数据配置
console.log('\n📋 检查数据配置...');
try {
    const dataPath = path.join(__dirname, 'src', 'data', 'index.ts');
    const dataContent = fs.readFileSync(dataPath, 'utf8');
    
    // 提取音频URL
    const audioUrlMatches = dataContent.match(/audioUrl:\s*["']([^"']+)["']/g);
    if (audioUrlMatches) {
        console.log('✅ 找到音频URL配置:');
        audioUrlMatches.slice(0, 5).forEach(match => {
            console.log(`   ${match}`);
        });
        if (audioUrlMatches.length > 5) {
            console.log(`   ... 还有 ${audioUrlMatches.length - 5} 个`);
        }
    } else {
        console.log('❌ 未找到音频URL配置');
    }
} catch (error) {
    console.log(`❌ 读取数据配置失败: ${error.message}`);
}

// 3. 检查Vite配置
console.log('\n⚙️  检查Vite配置...');
try {
    const viteConfigPath = path.join(__dirname, 'vite.config.ts');
    if (fs.existsSync(viteConfigPath)) {
        console.log('✅ vite.config.ts 存在');
        const viteConfig = fs.readFileSync(viteConfigPath, 'utf8');
        
        // 检查是否有静态资源配置
        if (viteConfig.includes('publicDir') || viteConfig.includes('assetsInclude')) {
            console.log('✅ 发现静态资源配置');
        } else {
            console.log('⚠️  未发现特殊的静态资源配置（使用默认配置）');
        }
    } else {
        console.log('❌ vite.config.ts 不存在');
    }
} catch (error) {
    console.log(`❌ 检查Vite配置失败: ${error.message}`);
}

// 4. 生成测试URL
console.log('\n🌐 生成测试URL...');
const testUrls = [
    'http://localhost:5173/audio/01.mp3',
    'http://localhost:5173/audio/02.mp3',
    'http://localhost:5173/audio/03.mp3',
    'http://localhost:5173/audio/04.mp3',
    'http://localhost:5173/audio/05.mp3'
];

console.log('📝 请在浏览器中测试以下URL:');
testUrls.forEach(url => {
    console.log(`   ${url}`);
});

// 5. 常见问题检查
console.log('\n🔧 常见问题检查...');

// 检查文件权限
try {
    const firstAudioFile = path.join(audioDir, '01.mp3');
    if (fs.existsSync(firstAudioFile)) {
        fs.accessSync(firstAudioFile, fs.constants.R_OK);
        console.log('✅ 音频文件可读');
    }
} catch (error) {
    console.log('❌ 音频文件权限问题:', error.message);
}

// 检查文件大小
try {
    const audioFiles = fs.readdirSync(audioDir).filter(file => file.endsWith('.mp3'));
    const suspiciousFiles = audioFiles.filter(file => {
        const filePath = path.join(audioDir, file);
        const stats = fs.statSync(filePath);
        return stats.size < 1000; // 小于1KB的文件可能有问题
    });
    
    if (suspiciousFiles.length > 0) {
        console.log('⚠️  发现可疑的小文件（可能损坏）:');
        suspiciousFiles.forEach(file => console.log(`   ${file}`));
    } else {
        console.log('✅ 所有音频文件大小正常');
    }
} catch (error) {
    console.log('❌ 检查文件大小失败:', error.message);
}

console.log('\n🚀 诊断完成！');
console.log('\n💡 建议的调试步骤:');
console.log('1. 启动开发服务器: npm run dev');
console.log('2. 在浏览器中打开: http://localhost:5173/audio-test.html');
console.log('3. 测试音频文件是否可以直接访问');
console.log('4. 检查浏览器控制台是否有错误信息');
console.log('5. 如果问题持续，请检查网络面板中的请求状态');