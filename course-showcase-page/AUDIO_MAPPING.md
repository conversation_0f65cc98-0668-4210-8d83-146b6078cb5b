# 音频文件映射表

## 📁 文件存放位置
将所有音频文件放在：`course-showcase-page/public/audio/` 目录下

## 🎵 音频文件对应表

### 理论篇 (3节课)
- **01.mp3** → 心理学的起源与发展 (30分钟)
- **02.mp3** → 心理学主要流派简介 (35分钟)  
- **03.mp3** → 常见心理问题与特点速览 (25分钟)

### 心法篇 (3节课)
- **04.mp3** → 导论：你的过去，是最好的起点 (20分钟)
- **05.mp3** → 重新定义"专业"：AI时代的咨询师新模型 (30分钟)
- **06.mp3** → 建立边界感：从"热心肠"到"专业助人者" (25分钟)

### 技法篇 (4节课)
- **07.mp3** → 首次链接：如何30分钟建立信任感？ (40分钟)
- **08.mp3** → 咨询过程：结构化咨询的"四步法" (45分钟)
- **09.mp3** → 提问的艺术：成为"问题"的专家 (35分钟)
- **10.mp3** → 收尾总结：让来访者带着希望离开的结构 (30分钟)

### AI篇 (3节课)
- **11.mp3** → 咨询前准备：AI帮你做案例预判和方案设计 (35分钟)
- **12.mp3** → 咨询中辅助：实时话术提醒和情绪识别 (40分钟)
- **13.mp3** → 咨询后复盘：AI协助案例分析和成长记录 (30分钟)

### 商业篇 (3节课)
- **14.mp3** → 如何找到前100个练手机会 (45分钟)
- **15.mp3** → 定价策略和服务边界设定 (35分钟)
- **16.mp3** → 危机干预和转介绍流程 (30分钟)

## 📋 操作步骤

1. **创建音频目录**：
   ```bash
   mkdir -p course-showcase-page/public/audio
   ```

2. **复制音频文件**：
   将你的音频文件按照上述映射表重命名并复制到 `course-showcase-page/public/audio/` 目录

3. **文件格式要求**：
   - 格式：MP3
   - 编码：建议使用 128kbps 或更高质量
   - 文件大小：建议每个文件不超过 50MB

4. **测试音频播放**：
   - 启动开发服务器：`npm run dev`
   - 点击任意课程章节测试音频播放功能

## 🔧 如果需要调整

如果你的音频文件顺序或内容与上述映射不符，可以：

1. **重新命名文件**：按照映射表重命名你的音频文件
2. **修改数据文件**：编辑 `src/data/index.ts` 中的 `audioUrl` 路径
3. **更新时长**：根据实际音频长度更新 `duration` 字段（以秒为单位）

## 📝 注意事项

- 音频文件路径是相对于 `public` 目录的
- 确保文件名与代码中的路径完全匹配
- 建议在上传前测试所有音频文件的播放功能
- 如果音频文件较大，考虑使用音频压缩工具优化文件大小