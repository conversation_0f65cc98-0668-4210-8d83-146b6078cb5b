const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');

class PasswordManager {
  constructor() {
    this.passwordFile = path.join(__dirname, '../config/password.json');
    this.defaultPassword = 'dabai';
    this.currentPassword = this.defaultPassword;
    
    this.init();
  }

  async init() {
    try {
      // 确保配置目录存在
      await this.ensureConfigDir();
      
      // 加载密码配置
      await this.loadPassword();
      
      console.log('🔐 密码管理器初始化完成');
    } catch (error) {
      console.error('❌ 密码管理器初始化失败:', error);
    }
  }

  async ensureConfigDir() {
    const configDir = path.dirname(this.passwordFile);
    try {
      await fs.access(configDir);
    } catch {
      await fs.mkdir(configDir, { recursive: true });
      console.log('📁 创建配置目录:', configDir);
    }
  }

  // 加载密码配置
  async loadPassword() {
    try {
      const data = await fs.readFile(this.passwordFile, 'utf8');
      const config = JSON.parse(data);
      this.currentPassword = config.password || this.defaultPassword;
      console.log('🔑 密码配置加载成功');
    } catch (error) {
      // 文件不存在，使用默认密码并创建配置文件
      console.log('📝 使用默认密码，创建密码配置文件');
      await this.savePassword(this.defaultPassword);
    }
  }

  // 保存密码配置
  async savePassword(password) {
    try {
      const config = {
        password: password,
        lastModified: new Date().toISOString(),
        version: '1.0'
      };
      
      await fs.writeFile(this.passwordFile, JSON.stringify(config, null, 2), 'utf8');
      this.currentPassword = password;
      console.log('💾 密码配置保存成功');
      return true;
    } catch (error) {
      console.error('❌ 保存密码配置失败:', error);
      throw error;
    }
  }

  // 验证密码
  verifyPassword(password) {
    return password === this.currentPassword;
  }

  // 修改密码
  async changePassword(oldPassword, newPassword) {
    try {
      // 验证旧密码
      if (!this.verifyPassword(oldPassword)) {
        throw new Error('旧密码错误');
      }

      // 验证新密码格式
      if (!this.validatePassword(newPassword)) {
        throw new Error('新密码格式不符合要求');
      }

      // 保存新密码
      await this.savePassword(newPassword);
      
      console.log('🔄 密码修改成功');
      return {
        success: true,
        message: '密码修改成功',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ 密码修改失败:', error);
      throw error;
    }
  }

  // 验证密码格式
  validatePassword(password) {
    // 密码要求：长度4-20位，包含字母或数字
    if (!password || typeof password !== 'string') {
      return false;
    }
    
    if (password.length < 4 || password.length > 20) {
      return false;
    }
    
    // 至少包含字母或数字
    const hasAlphaNumeric = /^[a-zA-Z0-9\u4e00-\u9fa5]+$/.test(password);
    return hasAlphaNumeric;
  }

  // 重置密码为默认值
  async resetPassword() {
    try {
      await this.savePassword(this.defaultPassword);
      console.log('🔄 密码已重置为默认值');
      return {
        success: true,
        message: '密码已重置为默认值',
        defaultPassword: this.defaultPassword
      };
    } catch (error) {
      console.error('❌ 密码重置失败:', error);
      throw error;
    }
  }

  // 获取密码信息（不包含实际密码）
  async getPasswordInfo() {
    try {
      const data = await fs.readFile(this.passwordFile, 'utf8');
      const config = JSON.parse(data);
      
      return {
        isDefault: this.currentPassword === this.defaultPassword,
        lastModified: config.lastModified,
        version: config.version,
        passwordLength: this.currentPassword.length
      };
    } catch (error) {
      return {
        isDefault: true,
        lastModified: null,
        version: '1.0',
        passwordLength: this.defaultPassword.length
      };
    }
  }

  // 生成安全的随机密码
  generateRandomPassword(length = 8) {
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let password = '';
    
    for (let i = 0; i < length; i++) {
      const randomIndex = crypto.randomInt(0, charset.length);
      password += charset[randomIndex];
    }
    
    return password;
  }

  // 导出密码配置
  async exportPasswordConfig(exportPath) {
    try {
      const configData = await fs.readFile(this.passwordFile, 'utf8');
      const exportFile = path.join(exportPath, `password-config-${Date.now()}.json`);
      
      await fs.writeFile(exportFile, configData, 'utf8');
      
      console.log(`📤 密码配置导出成功: ${exportFile}`);
      return {
        success: true,
        exportFile,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ 导出密码配置失败:', error);
      throw error;
    }
  }

  // 导入密码配置
  async importPasswordConfig(importFile) {
    try {
      const data = await fs.readFile(importFile, 'utf8');
      const config = JSON.parse(data);
      
      if (!config.password) {
        throw new Error('无效的密码配置文件');
      }
      
      // 验证密码格式
      if (!this.validatePassword(config.password)) {
        throw new Error('导入的密码格式不符合要求');
      }
      
      // 保存导入的密码
      await this.savePassword(config.password);
      
      console.log('📥 密码配置导入成功');
      return {
        success: true,
        message: '密码配置导入成功',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ 导入密码配置失败:', error);
      throw error;
    }
  }

  // 获取当前密码（仅用于内部验证）
  getCurrentPassword() {
    return this.currentPassword;
  }
}

module.exports = PasswordManager;
