const fs = require('fs').promises;
const path = require('path');
const { createReadStream, createWriteStream } = require('fs');
const { pipeline } = require('stream');
const { promisify } = require('util');
const pipelineAsync = promisify(pipeline);

class BackupManager {
  constructor() {
    this.configDir = path.join(__dirname, '../public/config');
    this.backupDir = path.join(__dirname, '../backups');
    this.maxBackups = 50; // 保留最多50个备份
    this.autoBackupInterval = 5 * 60 * 1000; // 5分钟自动备份一次
    this.isAutoBackupEnabled = true;
    
    this.init();
  }

  async init() {
    try {
      // 确保备份目录存在
      await this.ensureBackupDir();
      
      // 启动自动备份
      if (this.isAutoBackupEnabled) {
        this.startAutoBackup();
      }
      
      console.log('📦 备份管理器初始化完成');
    } catch (error) {
      console.error('❌ 备份管理器初始化失败:', error);
    }
  }

  async ensureBackupDir() {
    try {
      await fs.access(this.backupDir);
    } catch {
      await fs.mkdir(this.backupDir, { recursive: true });
      console.log('📁 创建备份目录:', this.backupDir);
    }
  }

  // 生成备份文件名
  generateBackupName(type = 'auto') {
    const now = new Date();
    const timestamp = now.toISOString().replace(/[:.]/g, '-').slice(0, -5);
    return `backup-${type}-${timestamp}`;
  }

  // 创建单个配置文件备份
  async backupSingleConfig(configName, backupName) {
    const sourceFile = path.join(this.configDir, `${configName}.json`);
    const backupFile = path.join(this.backupDir, `${backupName}-${configName}.json`);
    
    try {
      await fs.access(sourceFile);
      await fs.copyFile(sourceFile, backupFile);
      return backupFile;
    } catch (error) {
      console.warn(`⚠️ 无法备份配置文件 ${configName}:`, error.message);
      return null;
    }
  }

  // 创建完整备份
  async createBackup(type = 'manual', description = '') {
    const backupName = this.generateBackupName(type);
    const backupInfo = {
      name: backupName,
      type,
      description,
      timestamp: new Date().toISOString(),
      files: []
    };

    try {
      // 备份所有配置文件
      const configFiles = ['basic-config', 'course-config', 'business-config'];
      
      for (const configName of configFiles) {
        const backupFile = await this.backupSingleConfig(configName, backupName);
        if (backupFile) {
          backupInfo.files.push({
            original: `${configName}.json`,
            backup: path.basename(backupFile),
            size: (await fs.stat(backupFile)).size
          });
        }
      }

      // 保存备份信息
      const infoFile = path.join(this.backupDir, `${backupName}-info.json`);
      await fs.writeFile(infoFile, JSON.stringify(backupInfo, null, 2));

      // 清理旧备份
      await this.cleanupOldBackups();

      console.log(`✅ 备份创建成功: ${backupName}`);
      return backupInfo;
    } catch (error) {
      console.error('❌ 创建备份失败:', error);
      throw error;
    }
  }

  // 获取所有备份列表
  async getBackupList() {
    try {
      const files = await fs.readdir(this.backupDir);
      const infoFiles = files.filter(file => file.endsWith('-info.json'));
      
      const backups = [];
      for (const infoFile of infoFiles) {
        try {
          const infoPath = path.join(this.backupDir, infoFile);
          const content = await fs.readFile(infoPath, 'utf8');
          const backupInfo = JSON.parse(content);
          
          // 检查备份文件是否完整
          const isComplete = await this.verifyBackup(backupInfo);
          backupInfo.isComplete = isComplete;
          
          backups.push(backupInfo);
        } catch (error) {
          console.warn(`⚠️ 读取备份信息失败: ${infoFile}`, error.message);
        }
      }

      // 按时间倒序排列
      return backups.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    } catch (error) {
      console.error('❌ 获取备份列表失败:', error);
      return [];
    }
  }

  // 验证备份完整性
  async verifyBackup(backupInfo) {
    try {
      for (const file of backupInfo.files) {
        const backupFile = path.join(this.backupDir, file.backup);
        await fs.access(backupFile);
      }
      return true;
    } catch {
      return false;
    }
  }

  // 恢复备份
  async restoreBackup(backupName) {
    try {
      const infoFile = path.join(this.backupDir, `${backupName}-info.json`);
      const content = await fs.readFile(infoFile, 'utf8');
      const backupInfo = JSON.parse(content);

      // 验证备份完整性
      const isComplete = await this.verifyBackup(backupInfo);
      if (!isComplete) {
        throw new Error('备份文件不完整，无法恢复');
      }

      // 创建恢复前的备份
      await this.createBackup('pre-restore', `恢复前自动备份 - 恢复到 ${backupName}`);

      // 恢复配置文件
      const restoredFiles = [];
      for (const file of backupInfo.files) {
        const backupFile = path.join(this.backupDir, file.backup);
        const targetFile = path.join(this.configDir, file.original);
        
        await fs.copyFile(backupFile, targetFile);
        restoredFiles.push(file.original);
      }

      console.log(`✅ 备份恢复成功: ${backupName}`);
      return {
        success: true,
        backupName,
        restoredFiles,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ 恢复备份失败:', error);
      throw error;
    }
  }

  // 删除备份
  async deleteBackup(backupName) {
    try {
      const infoFile = path.join(this.backupDir, `${backupName}-info.json`);
      const content = await fs.readFile(infoFile, 'utf8');
      const backupInfo = JSON.parse(content);

      // 删除所有相关文件
      const filesToDelete = [
        `${backupName}-info.json`,
        ...backupInfo.files.map(f => f.backup)
      ];

      for (const fileName of filesToDelete) {
        const filePath = path.join(this.backupDir, fileName);
        try {
          await fs.unlink(filePath);
        } catch (error) {
          console.warn(`⚠️ 删除文件失败: ${fileName}`, error.message);
        }
      }

      console.log(`🗑️ 备份删除成功: ${backupName}`);
      return true;
    } catch (error) {
      console.error('❌ 删除备份失败:', error);
      throw error;
    }
  }

  // 清理旧备份
  async cleanupOldBackups() {
    try {
      const backups = await this.getBackupList();
      
      if (backups.length > this.maxBackups) {
        const backupsToDelete = backups
          .slice(this.maxBackups)
          .filter(backup => backup.type === 'auto'); // 只自动删除自动备份

        for (const backup of backupsToDelete) {
          await this.deleteBackup(backup.name);
        }

        if (backupsToDelete.length > 0) {
          console.log(`🧹 清理了 ${backupsToDelete.length} 个旧的自动备份`);
        }
      }
    } catch (error) {
      console.error('❌ 清理旧备份失败:', error);
    }
  }

  // 启动自动备份
  startAutoBackup() {
    if (this.autoBackupTimer) {
      clearInterval(this.autoBackupTimer);
    }

    this.autoBackupTimer = setInterval(async () => {
      try {
        await this.createBackup('auto', '自动备份');
      } catch (error) {
        console.error('❌ 自动备份失败:', error);
      }
    }, this.autoBackupInterval);

    console.log(`⏰ 自动备份已启动，间隔: ${this.autoBackupInterval / 1000 / 60} 分钟`);
  }

  // 停止自动备份
  stopAutoBackup() {
    if (this.autoBackupTimer) {
      clearInterval(this.autoBackupTimer);
      this.autoBackupTimer = null;
      console.log('⏹️ 自动备份已停止');
    }
  }

  // 设置自动备份间隔
  setAutoBackupInterval(minutes) {
    this.autoBackupInterval = minutes * 60 * 1000;
    if (this.isAutoBackupEnabled) {
      this.startAutoBackup();
    }
    console.log(`⏰ 自动备份间隔已设置为: ${minutes} 分钟`);
  }

  // 获取备份统计信息
  async getBackupStats() {
    try {
      const backups = await this.getBackupList();
      const stats = {
        total: backups.length,
        auto: backups.filter(b => b.type === 'auto').length,
        manual: backups.filter(b => b.type === 'manual').length,
        preRestore: backups.filter(b => b.type === 'pre-restore').length,
        totalSize: 0,
        oldestBackup: null,
        newestBackup: null
      };

      if (backups.length > 0) {
        stats.oldestBackup = backups[backups.length - 1];
        stats.newestBackup = backups[0];

        // 计算总大小
        for (const backup of backups) {
          for (const file of backup.files) {
            stats.totalSize += file.size || 0;
          }
        }
      }

      return stats;
    } catch (error) {
      console.error('❌ 获取备份统计失败:', error);
      return null;
    }
  }

  // 导出备份到指定目录
  async exportBackup(backupName, exportPath) {
    try {
      const infoFile = path.join(this.backupDir, `${backupName}-info.json`);
      const content = await fs.readFile(infoFile, 'utf8');
      const backupInfo = JSON.parse(content);

      // 确保导出目录存在
      await fs.mkdir(exportPath, { recursive: true });

      // 复制所有备份文件
      const exportedFiles = [];
      for (const file of backupInfo.files) {
        const sourceFile = path.join(this.backupDir, file.backup);
        const targetFile = path.join(exportPath, file.backup);
        
        await fs.copyFile(sourceFile, targetFile);
        exportedFiles.push(file.backup);
      }

      // 复制信息文件
      const targetInfoFile = path.join(exportPath, `${backupName}-info.json`);
      await fs.copyFile(infoFile, targetInfoFile);
      exportedFiles.push(`${backupName}-info.json`);

      console.log(`📤 备份导出成功: ${backupName} -> ${exportPath}`);
      return {
        success: true,
        backupName,
        exportPath,
        exportedFiles
      };
    } catch (error) {
      console.error('❌ 导出备份失败:', error);
      throw error;
    }
  }
}

module.exports = BackupManager;
