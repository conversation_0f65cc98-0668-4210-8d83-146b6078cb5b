const express = require('express');
const cors = require('cors');
const fs = require('fs').promises;
const path = require('path');
const multer = require('multer');
const BackupManager = require('./backup');

const app = express();
const PORT = 3001;

// 配置文件路径
const CONFIG_DIR = path.join(__dirname, '../public/config');
const UPLOAD_DIR = path.join(__dirname, '../public/images');

// 初始化备份管理器
const backupManager = new BackupManager();

// 中间件
app.use(cors());
app.use(express.json());
app.use('/images', express.static(path.join(__dirname, '../public/images')));

// 配置multer用于文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadPath = path.join(UPLOAD_DIR, req.body.folder || '');
    cb(null, uploadPath);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage: storage,
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif|svg/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);
    
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('只允许上传图片文件'));
    }
  },
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB限制
  }
});

// 简单的认证中间件
const authenticate = (req, res, next) => {
  const password = req.body?.password || req.query?.password;
  if (password !== 'dabai') {
    return res.status(401).json({ error: '密码错误' });
  }
  next();
};

// 读取配置文件
const readConfig = async (configName) => {
  try {
    const filePath = path.join(CONFIG_DIR, `${configName}.json`);
    const data = await fs.readFile(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    throw new Error(`读取配置文件失败: ${error.message}`);
  }
};

// 写入配置文件
const writeConfig = async (configName, data) => {
  try {
    const filePath = path.join(CONFIG_DIR, `${configName}.json`);

    // 在写入前创建备份
    try {
      await backupManager.createBackup('pre-change', `修改 ${configName} 前的备份`);
    } catch (backupError) {
      console.warn('⚠️ 创建备份失败，但继续写入配置:', backupError.message);
    }

    await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf8');
    return true;
  } catch (error) {
    throw new Error(`写入配置文件失败: ${error.message}`);
  }
};

// API路由

// 获取所有配置
app.get('/api/config/:type', async (req, res) => {
  try {
    const { type } = req.params;
    const config = await readConfig(type);
    res.json(config);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 更新配置
app.post('/api/config/:type', authenticate, async (req, res) => {
  try {
    const { type } = req.params;
    const { config } = req.body;
    
    await writeConfig(type, config);
    res.json({ success: true, message: '配置更新成功' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 文件上传
app.post('/api/upload', authenticate, upload.single('file'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: '没有上传文件' });
    }
    
    const folder = req.body.folder || '';
    const relativePath = `/images/${folder}${folder ? '/' : ''}${req.file.filename}`;
    
    res.json({
      success: true,
      message: '文件上传成功',
      url: relativePath,
      filename: req.file.filename
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 备份管理API

// 获取备份列表
app.get('/api/backups', authenticate, async (req, res) => {
  try {
    const backups = await backupManager.getBackupList();
    res.json({ success: true, backups });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 创建手动备份
app.post('/api/backups', authenticate, async (req, res) => {
  try {
    const { description = '手动备份' } = req.body;
    const backup = await backupManager.createBackup('manual', description);
    res.json({ success: true, backup });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 恢复备份
app.post('/api/backups/:backupName/restore', authenticate, async (req, res) => {
  try {
    const { backupName } = req.params;
    const result = await backupManager.restoreBackup(backupName);
    res.json({ success: true, result });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 删除备份
app.delete('/api/backups/:backupName', authenticate, async (req, res) => {
  try {
    const { backupName } = req.params;
    await backupManager.deleteBackup(backupName);
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 获取备份统计
app.get('/api/backups/stats', authenticate, async (req, res) => {
  try {
    const stats = await backupManager.getBackupStats();
    res.json({ success: true, stats });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 设置自动备份间隔
app.post('/api/backups/settings', authenticate, async (req, res) => {
  try {
    const { autoBackupInterval } = req.body;
    if (autoBackupInterval && autoBackupInterval > 0) {
      backupManager.setAutoBackupInterval(autoBackupInterval);
    }
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 导出备份
app.post('/api/backups/:backupName/export', authenticate, async (req, res) => {
  try {
    const { backupName } = req.params;
    const { exportPath = path.join(__dirname, '../exports') } = req.body;

    const result = await backupManager.exportBackup(backupName, exportPath);
    res.json({ success: true, result });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 课程模块管理API

// 添加课程模块
app.post('/api/course/modules', authenticate, async (req, res) => {
  try {
    const { module } = req.body;
    const courseConfig = await readConfig('course-config');
    
    // 生成新的模块ID
    const newId = `module-${Date.now()}`;
    const newModule = {
      id: newId,
      ...module,
      order: courseConfig.modules.length + 1,
      lessons: []
    };
    
    courseConfig.modules.push(newModule);
    await writeConfig('course-config', courseConfig);
    
    res.json({ success: true, module: newModule });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 删除课程模块
app.delete('/api/course/modules/:moduleId', authenticate, async (req, res) => {
  try {
    const { moduleId } = req.params;
    const courseConfig = await readConfig('course-config');
    
    courseConfig.modules = courseConfig.modules.filter(m => m.id !== moduleId);
    
    // 重新排序
    courseConfig.modules.forEach((module, index) => {
      module.order = index + 1;
    });
    
    await writeConfig('course-config', courseConfig);
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 更新模块排序
app.post('/api/course/modules/reorder', authenticate, async (req, res) => {
  try {
    const { moduleIds } = req.body;
    const courseConfig = await readConfig('course-config');
    
    // 根据新的顺序重新排列模块
    const reorderedModules = [];
    moduleIds.forEach((id, index) => {
      const module = courseConfig.modules.find(m => m.id === id);
      if (module) {
        module.order = index + 1;
        reorderedModules.push(module);
      }
    });
    
    courseConfig.modules = reorderedModules;
    await writeConfig('course-config', courseConfig);
    
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 课程管理API

// 添加课程
app.post('/api/course/modules/:moduleId/lessons', authenticate, async (req, res) => {
  try {
    const { moduleId } = req.params;
    const { lesson } = req.body;
    const courseConfig = await readConfig('course-config');
    
    const module = courseConfig.modules.find(m => m.id === moduleId);
    if (!module) {
      return res.status(404).json({ error: '模块不存在' });
    }
    
    const newLesson = {
      id: `lesson-${Date.now()}`,
      ...lesson,
      order: module.lessons.length + 1,
      isCompleted: false
    };
    
    module.lessons.push(newLesson);
    await writeConfig('course-config', courseConfig);
    
    res.json({ success: true, lesson: newLesson });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 删除课程
app.delete('/api/course/modules/:moduleId/lessons/:lessonId', authenticate, async (req, res) => {
  try {
    const { moduleId, lessonId } = req.params;
    const courseConfig = await readConfig('course-config');
    
    const module = courseConfig.modules.find(m => m.id === moduleId);
    if (!module) {
      return res.status(404).json({ error: '模块不存在' });
    }
    
    module.lessons = module.lessons.filter(l => l.id !== lessonId);
    
    // 重新排序
    module.lessons.forEach((lesson, index) => {
      lesson.order = index + 1;
    });
    
    await writeConfig('course-config', courseConfig);
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 更新课程排序
app.post('/api/course/modules/:moduleId/lessons/reorder', authenticate, async (req, res) => {
  try {
    const { moduleId } = req.params;
    const { lessonIds } = req.body;
    const courseConfig = await readConfig('course-config');
    
    const module = courseConfig.modules.find(m => m.id === moduleId);
    if (!module) {
      return res.status(404).json({ error: '模块不存在' });
    }
    
    // 根据新的顺序重新排列课程
    const reorderedLessons = [];
    lessonIds.forEach((id, index) => {
      const lesson = module.lessons.find(l => l.id === id);
      if (lesson) {
        lesson.order = index + 1;
        reorderedLessons.push(lesson);
      }
    });
    
    module.lessons = reorderedLessons;
    await writeConfig('course-config', courseConfig);
    
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`课程管理服务器运行在 http://localhost:${PORT}`);
});

module.exports = app;
