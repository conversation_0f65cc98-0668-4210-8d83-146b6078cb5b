const express = require('express');
const cors = require('cors');
const fs = require('fs').promises;
const path = require('path');
const multer = require('multer');
const jwt = require('jsonwebtoken');
const BackupManager = require('./backup');
const PasswordManager = require('./passwordManager');

const app = express();
const PORT = 3001;

// JWT密钥 - 在生产环境中应该使用环境变量
const JWT_SECRET = 'course-management-secret-key-2024';
const JWT_EXPIRES_IN = '24h'; // 24小时有效期

// 配置文件路径
const CONFIG_DIR = path.join(__dirname, '../public/config');
const UPLOAD_DIR = path.join(__dirname, '../public/images');

// 初始化管理器
const passwordManager = new PasswordManager();
const backupManager = new BackupManager();

// 中间件
app.use(cors());
app.use(express.json());
app.use('/images', express.static(path.join(__dirname, '../public/images')));

// 配置multer用于文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadPath = path.join(UPLOAD_DIR, req.body.folder || '');
    cb(null, uploadPath);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage: storage,
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif|svg/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);
    
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('只允许上传图片文件'));
    }
  },
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB限制
  }
});

// 认证中间件 - 支持密码和token两种方式
const authenticate = (req, res, next) => {
  // 优先检查token
  const token = req.headers.authorization?.replace('Bearer ', '') || req.body?.token || req.query?.token;

  if (token) {
    try {
      const decoded = jwt.verify(token, JWT_SECRET);
      req.user = decoded;
      return next();
    } catch (error) {
      // token无效，继续检查密码
    }
  }

  // 检查密码
  const password = req.body?.password || req.query?.password;
  if (password && passwordManager.verifyPassword(password)) {
    return next();
  }

  return res.status(401).json({ error: '认证失败，请重新登录' });
};

// 读取配置文件
const readConfig = async (configName) => {
  try {
    const filePath = path.join(CONFIG_DIR, `${configName}.json`);
    const data = await fs.readFile(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    throw new Error(`读取配置文件失败: ${error.message}`);
  }
};

// 写入配置文件
const writeConfig = async (configName, data) => {
  try {
    const filePath = path.join(CONFIG_DIR, `${configName}.json`);

    // 在写入前创建备份
    try {
      await backupManager.createBackup('pre-change', `修改 ${configName} 前的备份`);
    } catch (backupError) {
      console.warn('⚠️ 创建备份失败，但继续写入配置:', backupError.message);
    }

    await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf8');
    return true;
  } catch (error) {
    throw new Error(`写入配置文件失败: ${error.message}`);
  }
};

// API路由

// 获取所有配置
app.get('/api/config/:type', async (req, res) => {
  try {
    const { type } = req.params;
    const config = await readConfig(type);
    res.json(config);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 更新配置
app.post('/api/config/:type', authenticate, async (req, res) => {
  try {
    const { type } = req.params;
    const { config } = req.body;
    
    await writeConfig(type, config);
    res.json({ success: true, message: '配置更新成功' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 文件上传
app.post('/api/upload', authenticate, upload.single('file'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: '没有上传文件' });
    }
    
    const folder = req.body.folder || '';
    const relativePath = `/images/${folder}${folder ? '/' : ''}${req.file.filename}`;
    
    res.json({
      success: true,
      message: '文件上传成功',
      url: relativePath,
      filename: req.file.filename
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 密码管理API

// 验证登录
app.post('/api/auth/login', async (req, res) => {
  try {
    const { password } = req.body;
    const isValid = passwordManager.verifyPassword(password);

    if (isValid) {
      // 生成JWT token
      const token = jwt.sign(
        {
          authenticated: true,
          loginTime: new Date().toISOString()
        },
        JWT_SECRET,
        { expiresIn: JWT_EXPIRES_IN }
      );

      res.json({
        success: true,
        message: '登录成功',
        token,
        expiresIn: JWT_EXPIRES_IN
      });
    } else {
      res.status(401).json({ success: false, error: '密码错误' });
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// 验证token有效性
app.post('/api/auth/verify', async (req, res) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '') || req.body?.token;

    if (!token) {
      return res.status(401).json({ success: false, error: '缺少token' });
    }

    const decoded = jwt.verify(token, JWT_SECRET);
    res.json({
      success: true,
      valid: true,
      user: decoded
    });
  } catch (error) {
    res.status(401).json({
      success: false,
      valid: false,
      error: 'token已过期或无效'
    });
  }
});

// 刷新token
app.post('/api/auth/refresh', authenticate, async (req, res) => {
  try {
    // 生成新的token
    const newToken = jwt.sign(
      {
        authenticated: true,
        loginTime: new Date().toISOString()
      },
      JWT_SECRET,
      { expiresIn: JWT_EXPIRES_IN }
    );

    res.json({
      success: true,
      token: newToken,
      expiresIn: JWT_EXPIRES_IN
    });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// 获取密码信息
app.get('/api/password/info', authenticate, async (req, res) => {
  try {
    const info = await passwordManager.getPasswordInfo();
    res.json({ success: true, info });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 修改密码
app.post('/api/password/change', authenticate, async (req, res) => {
  try {
    const { oldPassword, newPassword } = req.body;
    const result = await passwordManager.changePassword(oldPassword, newPassword);
    res.json({ success: true, result });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

// 重置密码
app.post('/api/password/reset', authenticate, async (req, res) => {
  try {
    const result = await passwordManager.resetPassword();
    res.json({ success: true, result });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 生成随机密码
app.post('/api/password/generate', authenticate, async (req, res) => {
  try {
    const { length = 8 } = req.body;
    const randomPassword = passwordManager.generateRandomPassword(length);
    res.json({ success: true, password: randomPassword });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 验证密码格式
app.post('/api/password/validate', authenticate, async (req, res) => {
  try {
    const { password } = req.body;
    const isValid = passwordManager.validatePassword(password);
    res.json({ success: true, isValid });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 备份管理API

// 获取备份列表
app.get('/api/backups', authenticate, async (req, res) => {
  try {
    const backups = await backupManager.getBackupList();
    res.json({ success: true, backups });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 创建手动备份
app.post('/api/backups', authenticate, async (req, res) => {
  try {
    const { description = '手动备份' } = req.body;
    const backup = await backupManager.createBackup('manual', description);
    res.json({ success: true, backup });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 恢复备份
app.post('/api/backups/:backupName/restore', authenticate, async (req, res) => {
  try {
    const { backupName } = req.params;
    const result = await backupManager.restoreBackup(backupName);
    res.json({ success: true, result });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 删除备份
app.delete('/api/backups/:backupName', authenticate, async (req, res) => {
  try {
    const { backupName } = req.params;
    await backupManager.deleteBackup(backupName);
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 获取备份统计
app.get('/api/backups/stats', authenticate, async (req, res) => {
  try {
    const stats = await backupManager.getBackupStats();
    res.json({ success: true, stats });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 设置自动备份配置
app.post('/api/backups/settings', authenticate, async (req, res) => {
  try {
    const { autoBackupInterval, enableAutoBackup } = req.body;

    // 设置自动备份开关
    if (typeof enableAutoBackup === 'boolean') {
      if (enableAutoBackup) {
        backupManager.enableAutoBackup();
      } else {
        backupManager.disableAutoBackup();
      }
    }

    // 设置自动备份间隔
    if (autoBackupInterval && autoBackupInterval > 0) {
      backupManager.setAutoBackupInterval(autoBackupInterval);
    }

    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 导出备份
app.post('/api/backups/:backupName/export', authenticate, async (req, res) => {
  try {
    const { backupName } = req.params;
    const { exportPath = path.join(__dirname, '../exports') } = req.body;

    const result = await backupManager.exportBackup(backupName, exportPath);
    res.json({ success: true, result });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 课程模块管理API

// 添加课程模块
app.post('/api/course/modules', authenticate, async (req, res) => {
  try {
    const { module } = req.body;
    const courseConfig = await readConfig('course-config');
    
    // 生成新的模块ID
    const newId = `module-${Date.now()}`;
    const newModule = {
      id: newId,
      ...module,
      order: courseConfig.modules.length + 1,
      lessons: []
    };
    
    courseConfig.modules.push(newModule);
    await writeConfig('course-config', courseConfig);
    
    res.json({ success: true, module: newModule });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 删除课程模块
app.delete('/api/course/modules/:moduleId', authenticate, async (req, res) => {
  try {
    const { moduleId } = req.params;
    const courseConfig = await readConfig('course-config');
    
    courseConfig.modules = courseConfig.modules.filter(m => m.id !== moduleId);
    
    // 重新排序
    courseConfig.modules.forEach((module, index) => {
      module.order = index + 1;
    });
    
    await writeConfig('course-config', courseConfig);
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 更新模块排序
app.post('/api/course/modules/reorder', authenticate, async (req, res) => {
  try {
    const { moduleIds } = req.body;
    const courseConfig = await readConfig('course-config');
    
    // 根据新的顺序重新排列模块
    const reorderedModules = [];
    moduleIds.forEach((id, index) => {
      const module = courseConfig.modules.find(m => m.id === id);
      if (module) {
        module.order = index + 1;
        reorderedModules.push(module);
      }
    });
    
    courseConfig.modules = reorderedModules;
    await writeConfig('course-config', courseConfig);
    
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 课程管理API

// 添加课程
app.post('/api/course/modules/:moduleId/lessons', authenticate, async (req, res) => {
  try {
    const { moduleId } = req.params;
    const { lesson } = req.body;
    const courseConfig = await readConfig('course-config');
    
    const module = courseConfig.modules.find(m => m.id === moduleId);
    if (!module) {
      return res.status(404).json({ error: '模块不存在' });
    }
    
    const newLesson = {
      id: `lesson-${Date.now()}`,
      ...lesson,
      order: module.lessons.length + 1,
      isCompleted: false
    };
    
    module.lessons.push(newLesson);
    await writeConfig('course-config', courseConfig);
    
    res.json({ success: true, lesson: newLesson });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 删除课程
app.delete('/api/course/modules/:moduleId/lessons/:lessonId', authenticate, async (req, res) => {
  try {
    const { moduleId, lessonId } = req.params;
    const courseConfig = await readConfig('course-config');
    
    const module = courseConfig.modules.find(m => m.id === moduleId);
    if (!module) {
      return res.status(404).json({ error: '模块不存在' });
    }
    
    module.lessons = module.lessons.filter(l => l.id !== lessonId);
    
    // 重新排序
    module.lessons.forEach((lesson, index) => {
      lesson.order = index + 1;
    });
    
    await writeConfig('course-config', courseConfig);
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 更新课程排序
app.post('/api/course/modules/:moduleId/lessons/reorder', authenticate, async (req, res) => {
  try {
    const { moduleId } = req.params;
    const { lessonIds } = req.body;
    const courseConfig = await readConfig('course-config');
    
    const module = courseConfig.modules.find(m => m.id === moduleId);
    if (!module) {
      return res.status(404).json({ error: '模块不存在' });
    }
    
    // 根据新的顺序重新排列课程
    const reorderedLessons = [];
    lessonIds.forEach((id, index) => {
      const lesson = module.lessons.find(l => l.id === id);
      if (lesson) {
        lesson.order = index + 1;
        reorderedLessons.push(lesson);
      }
    });
    
    module.lessons = reorderedLessons;
    await writeConfig('course-config', courseConfig);
    
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`课程管理服务器运行在 http://localhost:${PORT}`);
});

module.exports = app;
