{"name": "course-showcase-page", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:debug": "vite --open", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "check-audio": "node check-audio.js", "debug-audio": "node debug-audio.js"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.7.1", "react-use": "^17.6.0", "styled-components": "^6.1.19"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/node": "^24.2.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/styled-components": "^5.1.34", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}