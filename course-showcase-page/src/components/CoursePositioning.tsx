import React, { useState } from 'react';
import styled, { css } from 'styled-components';
import { Container, Typography } from './ui';
import { useConfigContext } from '../contexts/ConfigContext';

interface CoursePositioningProps {
  className?: string;
}

interface ExpandableCardProps {
  title: string;
  subtitle: string;
  description: string;
  details: string[];
  isExpanded: boolean;
  onToggle: () => void;
}

const Section = styled.section`
  /* Mobile-first responsive padding */
  padding: ${({ theme }) => theme.spacing[12]} 0;
  background-color: ${({ theme }) => theme.colors.background};
  
  ${({ theme }) => theme.mediaQueries.tablet} {
    padding: ${({ theme }) => theme.spacing[16]} 0;
  }
  
  ${({ theme }) => theme.mediaQueries.desktop} {
    padding: ${({ theme }) => theme.spacing[20]} 0;
  }
`;

const SectionHeader = styled.div`
  text-align: center;
  margin-bottom: ${({ theme }) => theme.spacing[12]};
`;

const CoreSellingPoints = styled.div`
  background: linear-gradient(135deg, ${({ theme }) => theme.colors.growthGreen}15, ${({ theme }) => theme.colors.growthGreen}08);
  border: 2px solid ${({ theme }) => theme.colors.growthGreen};
  border-radius: ${({ theme }) => theme.borderRadius['2xl']};
  /* Mobile-first responsive padding */
  padding: ${({ theme }) => theme.spacing[6]};
  margin-bottom: ${({ theme }) => theme.spacing[8]};
  text-align: center;
  
  ${({ theme }) => theme.mediaQueries.tablet} {
    padding: ${({ theme }) => theme.spacing[8]};
    margin-bottom: ${({ theme }) => theme.spacing[12]};
  }
`;

const SellingPointsText = styled.div`
  /* Mobile-first responsive font sizing */
  font-size: ${({ theme }) => theme.typography.fontSizes.lg};
  font-weight: ${({ theme }) => theme.typography.fontWeights.bold};
  color: ${({ theme }) => theme.colors.growthGreen};
  font-family: ${({ theme }) => theme.typography.fonts.chinese};
  line-height: 1.4;
  
  ${({ theme }) => theme.mediaQueries.tablet} {
    font-size: ${({ theme }) => theme.typography.fontSizes.xl};
  }
  
  ${({ theme }) => theme.mediaQueries.desktop} {
    font-size: ${({ theme }) => theme.typography.fontSizes['2xl']};
  }
`;

const TargetAudienceGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${({ theme }) => theme.spacing[4]};
  
  /* Tablet: 2 columns with more space */
  ${({ theme }) => theme.mediaQueries.tablet} {
    grid-template-columns: 1fr 1fr;
    gap: ${({ theme }) => theme.spacing[6]};
  }
  
  /* Desktop: maintain 2 columns with optimal spacing */
  ${({ theme }) => theme.mediaQueries.desktop} {
    gap: ${({ theme }) => theme.spacing[8]};
  }
`;

const ExpandableCard = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== 'isExpanded'
})<{ isExpanded: boolean }>`
  border: 2px solid ${({ theme }) => theme.colors.growthGreen};
  background: linear-gradient(135deg, ${({ theme }) => theme.colors.white}, ${({ theme }) => theme.colors.growthGreen}05);
  cursor: pointer;
  transition: all ${({ theme }) => theme.animations.duration.normal} ${({ theme }) => theme.animations.easing.easeOut};
  border-radius: ${({ theme }) => theme.borderRadius.xl};
  padding: ${({ theme }) => theme.spacing[6]};
  box-shadow: ${({ theme }) => theme.shadows.base};
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px ${({ theme }) => theme.colors.growthGreen}20;
    border-color: ${({ theme }) => theme.colors.growthGreen};
  }
  
  &:focus {
    outline: 2px solid ${({ theme }) => theme.colors.trustBlue};
    outline-offset: 2px;
  }
`;

const CardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: ${({ theme }) => theme.spacing[4]};
`;

const CardTitle = styled(Typography)`
  color: ${({ theme }) => theme.colors.growthGreen};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
`;

const CardSubtitle = styled(Typography)`
  color: ${({ theme }) => theme.colors.gray[600]};
  margin-bottom: ${({ theme }) => theme.spacing[4]};
`;

const ExpandIcon = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== 'isExpanded'
})<{ isExpanded: boolean }>`
  /* Larger touch target for mobile */
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: ${({ theme }) => theme.colors.growthGreen};
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
  transition: transform ${({ theme }) => theme.animations.duration.normal} ${({ theme }) => theme.animations.easing.easeOut};
  flex-shrink: 0;
  margin-left: ${({ theme }) => theme.spacing[3]};
  
  /* Smaller on desktop */
  ${({ theme }) => theme.mediaQueries.desktop} {
    width: 24px;
    height: 24px;
    font-size: 18px;
    margin-left: ${({ theme }) => theme.spacing[4]};
  }
  
  ${({ isExpanded }) => isExpanded && css`
    transform: rotate(45deg);
  `}
`;

const CardDescription = styled(Typography)`
  color: ${({ theme }) => theme.colors.gray[700]};
  line-height: ${({ theme }) => theme.typography.lineHeights.relaxed};
`;

const ExpandedContent = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== 'isExpanded'
})<{ isExpanded: boolean }>`
  overflow: hidden;
  transition: all ${({ theme }) => theme.animations.duration.normal} ${({ theme }) => theme.animations.easing.easeOut};
  
  ${({ isExpanded }) => !isExpanded && css`
    max-height: 0;
    opacity: 0;
    margin-top: 0;
  `}
  
  ${({ isExpanded, theme }) => isExpanded && css`
    max-height: 500px;
    opacity: 1;
    margin-top: ${theme.spacing[6]};
  `}
`;

const DetailsList = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0;
`;

const DetailItem = styled.li`
  padding: ${({ theme }) => theme.spacing[3]} 0;
  border-bottom: 1px solid ${({ theme }) => theme.colors.growthGreen}20;
  color: ${({ theme }) => theme.colors.gray[700]};
  position: relative;
  padding-left: ${({ theme }) => theme.spacing[6]};
  
  &:before {
    content: '✓';
    position: absolute;
    left: 0;
    color: ${({ theme }) => theme.colors.growthGreen};
    font-weight: bold;
  }
  
  &:last-child {
    border-bottom: none;
  }
`;

const ExpandableCardComponent: React.FC<ExpandableCardProps> = ({
  title,
  subtitle,
  description,
  details,
  isExpanded,
  onToggle,
}) => {
  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      onToggle();
    }
  };

  return (
    <ExpandableCard
      isExpanded={isExpanded}
      onClick={onToggle}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="button"
      aria-expanded={isExpanded}
      aria-label={`${title} - ${isExpanded ? '收起' : '展开'}详细信息`}
    >
      <CardHeader>
        <div>
          <CardTitle variant="h4" chinese weight="bold">
            {title}
          </CardTitle>
          <CardSubtitle variant="body" chinese>
            {subtitle}
          </CardSubtitle>
        </div>
        <ExpandIcon isExpanded={isExpanded}>
          +
        </ExpandIcon>
      </CardHeader>
      
      <CardDescription variant="body" chinese>
        {description}
      </CardDescription>
      
      <ExpandedContent isExpanded={isExpanded}>
        <DetailsList>
          {details.map((detail, index) => (
            <DetailItem key={index}>
              <Typography variant="body" chinese>
                {detail}
              </Typography>
            </DetailItem>
          ))}
        </DetailsList>
      </ExpandedContent>
    </ExpandableCard>
  );
};

export const CoursePositioning: React.FC<CoursePositioningProps> = ({ className }) => {
  const { basicConfig, courseConfig, isLoading } = useConfigContext();
  const [expandedCards, setExpandedCards] = useState<{ [key: string]: boolean }>({
    education: false,
    certificate: false,
  });

  const toggleCard = (cardKey: string) => {
    setExpandedCards(prev => ({
      ...prev,
      [cardKey]: !prev[cardKey],
    }));
  };

  // Show loading state if config is not ready
  if (isLoading || !basicConfig || !courseConfig) {
    return (
      <Section
        className={className}
        id="positioning"
        aria-labelledby="positioning-heading"
        role="region"
      >
        <Container>
          <SectionHeader>
            <Typography
              variant="h2"
              chinese
              weight="bold"
              align="center"
              color="#374151"
            >
              加载中...
            </Typography>
          </SectionHeader>
        </Container>
      </Section>
    );
  }

  // Use configured target audiences with enhanced details
  const targetAudiences = courseConfig.positioning.targetAudiences.map((audience, index) => ({
    key: index === 0 ? 'education' : 'certificate',
    title: audience.title,
    subtitle: index === 0 ? '有经验基础，寻求新方向' : '有证书基础，缺乏实战经验',
    description: audience.description,
    details: index === 0 ? [
      '拥有教育行业3年以上工作经验',
      '具备基础的人际沟通和管理能力',
      '对心理咨询行业有浓厚兴趣',
      '希望实现职业转型和收入提升',
      '愿意投入时间学习新的专业技能',
    ] : [
      '已获得心理咨询师相关证书',
      '理论基础扎实但实践经验不足',
      '不知道如何获取和维护客户',
      '希望快速建立咨询实践能力',
      '需要系统的实战指导和支持',
    ],
  }));

  return (
    <Section 
      className={className}
      id="positioning"
      aria-labelledby="positioning-heading"
      role="region"
    >
      <Container>
        <SectionHeader>
          <Typography
            variant="h2"
            chinese
            weight="bold"
            align="center"
            color="#374151"
          >
            {basicConfig.sections.coursePositioning.title}
          </Typography>
          <Typography
            variant="body"
            chinese
            align="center"
            color="#6B7280"
            style={{ marginTop: '16px' }}
            aria-label={`课程定位说明：${basicConfig.sections.coursePositioning.subtitle}`}
          >
            {basicConfig.sections.coursePositioning.subtitle}
          </Typography>
        </SectionHeader>

        <CoreSellingPoints 
          role="banner"
          aria-labelledby="selling-points-heading"
        >
          <SellingPointsText
            id="selling-points-heading"
            role="heading"
            aria-level={3}
          >
            {courseConfig.positioning.coreSellingPoints.join(' + ')}
          </SellingPointsText>
          <Typography 
            variant="body" 
            chinese 
            align="center" 
            color="#6B7280" 
            style={{ marginTop: '16px' }}
            aria-label={`学习方法说明：${basicConfig.sections.coursePositioning.methodDescription}`}
          >
            {basicConfig.sections.coursePositioning.methodDescription}
          </Typography>
        </CoreSellingPoints>

        <TargetAudienceGrid 
          role="group"
          aria-label="目标受众群体"
        >
          {targetAudiences.map((audience) => (
            <ExpandableCardComponent
              key={audience.key}
              title={audience.title}
              subtitle={audience.subtitle}
              description={audience.description}
              details={audience.details}
              isExpanded={expandedCards[audience.key]}
              onToggle={() => toggleCard(audience.key)}
            />
          ))}
        </TargetAudienceGrid>
      </Container>
    </Section>
  );
};

export default CoursePositioning;