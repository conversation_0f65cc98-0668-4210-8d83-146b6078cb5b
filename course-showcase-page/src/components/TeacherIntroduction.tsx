import React from 'react';
import styled from 'styled-components';
import { Card, Typography, Container } from './ui';
import { LazyImage } from './LazyImage';
import { LazyContent } from './LazyContent';
import { teacherProfile } from '../data';

const TeacherSection = styled.section`
  /* Mobile-first responsive padding */
  padding: ${({ theme }) => theme.spacing[12]} 0;
  background-color: ${({ theme }) => theme.colors.warmSand};
  
  ${({ theme }) => theme.mediaQueries.tablet} {
    padding: ${({ theme }) => theme.spacing[16]} 0;
  }
  
  ${({ theme }) => theme.mediaQueries.desktop} {
    padding: ${({ theme }) => theme.spacing[20]} 0;
  }
`;

const TeacherCard = styled(Card)`
  max-width: 800px;
  margin: 0 auto;
  
  ${({ theme }) => theme.mediaQueries.desktop} {
    max-width: 900px;
  }
`;

const TeacherContent = styled.div`
  display: grid;
  gap: ${({ theme }) => theme.spacing[8]};
  
  ${({ theme }) => theme.mediaQueries.tablet} {
    grid-template-columns: 200px 1fr;
    gap: ${({ theme }) => theme.spacing[10]};
  }
  
  ${({ theme }) => theme.mediaQueries.desktop} {
    grid-template-columns: 240px 1fr;
    gap: ${({ theme }) => theme.spacing[12]};
  }
`;

const TeacherImageContainer = styled.div`
  display: flex;
  justify-content: center;
  
  ${({ theme }) => theme.mediaQueries.tablet} {
    justify-content: flex-start;
  }
`;

const TeacherImageWrapper = styled.div`
  width: 160px;
  height: 160px;
  border-radius: ${({ theme }) => theme.borderRadius.xl};
  overflow: hidden;
  
  ${({ theme }) => theme.mediaQueries.tablet} {
    width: 200px;
    height: 200px;
  }
  
  ${({ theme }) => theme.mediaQueries.desktop} {
    width: 240px;
    height: 240px;
  }
`;

const TeacherImagePlaceholder = styled.div`
  width: 100%;
  height: 100%;
  background-color: ${({ theme }) => theme.colors.gray[200]};
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${({ theme }) => theme.colors.gray[500]};
  font-family: ${({ theme }) => theme.typography.fonts.chinese};
  font-size: ${({ theme }) => theme.typography.fontSizes.sm};
  text-align: center;
`;

const TeacherInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[6]};
`;

const TeacherHeader = styled.div`
  text-align: center;
  
  ${({ theme }) => theme.mediaQueries.tablet} {
    text-align: left;
  }
`;

const TeacherName = styled(Typography)`
  color: ${({ theme }) => theme.colors.trustBlue};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
`;

const TeacherExperience = styled(Typography)`
  color: ${({ theme }) => theme.colors.trustBlue};
  opacity: 0.8;
`;

const TeacherStory = styled(Typography)`
  color: ${({ theme }) => theme.colors.gray[700]};
  line-height: ${({ theme }) => theme.typography.lineHeights.relaxed};
`;

const AchievementsGrid = styled.div`
  display: grid;
  gap: ${({ theme }) => theme.spacing[4]};
  /* Mobile: single column for better readability */
  grid-template-columns: 1fr;
  
  /* Small mobile: 2 columns when space allows */
  @media (min-width: 480px) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  /* Tablet and Desktop: 2 columns for consistent layout */
  ${({ theme }) => theme.mediaQueries.tablet} {
    grid-template-columns: repeat(2, 1fr);
    gap: ${({ theme }) => theme.spacing[5]};
  }
  
  ${({ theme }) => theme.mediaQueries.desktop} {
    grid-template-columns: repeat(2, 1fr);
    gap: ${({ theme }) => theme.spacing[6]};
  }
`;

const AchievementCard = styled.div`
  background-color: ${({ theme }) => theme.colors.white};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  padding: ${({ theme }) => theme.spacing[4]};
  text-align: center;
  border: 1px solid ${({ theme }) => theme.colors.gray[100]};
  transition: all ${({ theme }) => theme.animations.duration.normal} ${({ theme }) => theme.animations.easing.easeOut};
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  
  ${({ theme }) => theme.mediaQueries.tablet} {
    padding: ${({ theme }) => theme.spacing[5]};
    min-height: 140px;
    gap: ${({ theme }) => theme.spacing[3]};
  }
  
  ${({ theme }) => theme.mediaQueries.desktop} {
    padding: ${({ theme }) => theme.spacing[6]};
    min-height: 160px;
    gap: ${({ theme }) => theme.spacing[3]};
    /* 桌面端2列布局，卡片可以更大更舒适 */
  }
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: ${({ theme }) => theme.shadows.md};
  }
  
  /* Improve touch interaction on mobile */
  @media (max-width: 767px) {
    &:active {
      transform: scale(0.98);
    }
  }
`;

const AchievementValue = styled(Typography)`
  color: ${({ theme }) => theme.colors.actionOrange};
  margin: 0;
  line-height: 1.2;
  white-space: nowrap;
  
  ${({ theme }) => theme.mediaQueries.desktop} {
    font-size: ${({ theme }) => theme.typography.fontSizes['2xl']};
  }
`;

const AchievementMetric = styled(Typography)`
  color: ${({ theme }) => theme.colors.trustBlue};
  margin: 0;
  line-height: 1.3;
  text-align: center;
  
  ${({ theme }) => theme.mediaQueries.desktop} {
    font-size: ${({ theme }) => theme.typography.fontSizes.base};
  }
`;

const AchievementDescription = styled(Typography)`
  color: ${({ theme }) => theme.colors.gray[600]};
  margin: 0;
  line-height: 1.4;
  text-align: center;
  word-break: keep-all;
  
  ${({ theme }) => theme.mediaQueries.desktop} {
    font-size: ${({ theme }) => theme.typography.fontSizes.sm};
  }
`;

export const TeacherIntroduction: React.FC = () => {
  return (
    <TeacherSection
      id="teacher"
      aria-labelledby="teacher-heading"
      role="region"
    >
      <Container>
        <TeacherCard variant="elevated" padding="lg">
          <TeacherContent>
            <TeacherImageContainer>
              <TeacherImageWrapper>
                <LazyImage
                  src={teacherProfile.photoUrl || '/placeholder-teacher.jpg'}
                  alt={`${teacherProfile.name}的照片 - 40岁资深教育专家，拥有20年教育经验`}
                  placeholder={
                    <TeacherImagePlaceholder>
                      <div>
                        教师照片
                        <br />
                        石丽莉
                      </div>
                    </TeacherImagePlaceholder>
                  }
                  loading="lazy"
                />
              </TeacherImageWrapper>
            </TeacherImageContainer>

            <TeacherInfo>
              <TeacherHeader>
                <div>
                  <TeacherName
                    variant="h2"
                    chinese
                    weight="bold"
                  >
                    老师介绍：{teacherProfile.name}
                  </TeacherName>
                </div>
                <TeacherExperience
                  variant="h6"
                  chinese
                  weight="medium"
                  aria-label={`教师资历：${teacherProfile.experience}`}
                >
                  {teacherProfile.experience}
                </TeacherExperience>
              </TeacherHeader>

              <TeacherStory
                variant="body"
                chinese
                aria-label="教师个人经历和专业背景介绍"
              >
                {teacherProfile.story}
              </TeacherStory>

              <LazyContent
                placeholder={
                  <div style={{ textAlign: 'center', padding: '2rem' }}>
                    <div style={{ marginBottom: '1rem' }}>📊</div>
                    <div>成就数据加载中...</div>
                  </div>
                }
              >
                <AchievementsGrid
                  role="list"
                  aria-label="教师成就统计"
                >
                  {teacherProfile.achievements.map((achievement, index) => (
                    <AchievementCard
                      key={index}
                      role="listitem"
                      aria-label={`${achievement.metric}: ${achievement.value}, ${achievement.description}`}
                    >
                      <AchievementValue variant="h4" chinese weight="bold">
                        {achievement.value}
                      </AchievementValue>
                      <AchievementMetric variant="body" chinese weight="medium">
                        {achievement.metric}
                      </AchievementMetric>
                      <AchievementDescription variant="caption" chinese>
                        {achievement.description}
                      </AchievementDescription>
                    </AchievementCard>
                  ))}
                </AchievementsGrid>
              </LazyContent>
            </TeacherInfo>
          </TeacherContent>
        </TeacherCard>
      </Container>
    </TeacherSection>
  );
};

export default TeacherIntroduction;