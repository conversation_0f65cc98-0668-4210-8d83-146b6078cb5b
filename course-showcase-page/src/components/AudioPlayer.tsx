import React, { useEffect, useRef } from 'react';
import styled, { keyframes } from 'styled-components';
import { useAudio, useScreenReaderAnnouncement, useAccessibility } from '../hooks';
import { formatTime, calculateProgress } from '../utils/audioUtils';

// Animations
const slideUp = keyframes`
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
`;

const slideDown = keyframes`
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(100%);
    opacity: 0;
  }
`;

// Styled components
const PlayerContainer = styled.div<{ $isVisible: boolean }>`
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: ${({ theme }) => theme.colors.surface};
  border-top: 1px solid ${({ theme }) => theme.colors.border};
  box-shadow: ${({ theme }) => theme.shadows.lg};
  padding: ${({ theme }) => theme.spacing[4]};
  z-index: 1000;
  animation: ${({ $isVisible }) => ($isVisible ? slideUp : slideDown)} 
    ${({ theme }) => theme.animations.duration.normal} 
    ${({ theme }) => theme.animations.easing.easeOut};
  
  ${({ theme }) => theme.mediaQueries.desktop} {
    position: fixed;
    top: 50%;
    right: ${({ theme }) => theme.spacing[6]};
    bottom: auto;
    left: auto;
    width: 320px;
    transform: translateY(-50%);
    border-radius: ${({ theme }) => theme.borderRadius.xl};
    border: 1px solid ${({ theme }) => theme.colors.border};
  }
`;

const PlayerContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[3]};
  max-width: 600px;
  margin: 0 auto;
  
  ${({ theme }) => theme.mediaQueries.desktop} {
    max-width: none;
    margin: 0;
  }
`;

const LessonInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[1]};
  min-width: 0;
`;

const LessonTitle = styled.h4`
  font-family: ${({ theme }) => theme.typography.fonts.chinese};
  font-size: ${({ theme }) => theme.typography.fontSizes.sm};
  font-weight: ${({ theme }) => theme.typography.fontWeights.medium};
  color: ${({ theme }) => theme.colors.gray[800]};
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  
  ${({ theme }) => theme.mediaQueries.tablet} {
    font-size: ${({ theme }) => theme.typography.fontSizes.base};
  }
`;

const LessonDuration = styled.span`
  font-family: ${({ theme }) => theme.typography.fonts.english};
  font-size: ${({ theme }) => theme.typography.fontSizes.xs};
  color: ${({ theme }) => theme.colors.gray[600]};
`;

const PlaylistInfo = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  font-family: ${({ theme }) => theme.typography.fonts.english};
  font-size: ${({ theme }) => theme.typography.fontSizes.xs};
  color: ${({ theme }) => theme.colors.actionOrange};
`;

const CurrentLessonIndicator = styled.div`
  width: 8px;
  height: 8px;
  background: ${({ theme }) => theme.colors.actionOrange};
  border-radius: 50%;
  animation: pulse 2s infinite;
  
  @keyframes pulse {
    0% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
    100% {
      opacity: 1;
    }
  }
`;

const ControlsContainer = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
`;

const NavigationButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  /* Mobile-first: larger touch targets */
  width: ${({ theme }) => theme.touchTargets.minimum};
  height: ${({ theme }) => theme.touchTargets.minimum};
  border-radius: 50%;
  border: 1px solid ${({ theme }) => theme.colors.gray[300]};
  background: ${({ theme }) => theme.colors.surface};
  color: ${({ theme }) => theme.colors.gray[600]};
  cursor: pointer;
  transition: all ${({ theme }) => theme.animations.duration.normal} 
    ${({ theme }) => theme.animations.easing.easeOut};
  
  &:hover {
    background: ${({ theme }) => theme.colors.gray[50]};
    border-color: ${({ theme }) => theme.colors.trustBlue};
    color: ${({ theme }) => theme.colors.trustBlue};
  }
  
  &:active {
    transform: scale(0.95);
  }
  
  &:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    transform: none;
  }
  
  svg {
    width: 18px;
    height: 18px;
    
    /* Smaller icons on desktop */
    ${({ theme }) => theme.mediaQueries.desktop} {
      width: 16px;
      height: 16px;
    }
  }
  
  /* Smaller buttons on desktop */
  ${({ theme }) => theme.mediaQueries.desktop} {
    width: 36px;
    height: 36px;
  }
`;

const PlayButton = styled.button<{ $isPlaying: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  width: ${({ theme }) => theme.touchTargets.minimum};
  height: ${({ theme }) => theme.touchTargets.minimum};
  border-radius: 50%;
  border: none;
  background: ${({ theme, $isPlaying }) => 
    $isPlaying ? theme.colors.actionOrange : theme.colors.trustBlue};
  color: ${({ theme }) => theme.colors.white};
  cursor: pointer;
  transition: all ${({ theme }) => theme.animations.duration.normal} 
    ${({ theme }) => theme.animations.easing.easeOut};
  
  &:hover {
    transform: scale(1.05);
    box-shadow: ${({ theme }) => theme.shadows.md};
  }
  
  &:active {
    transform: scale(0.95);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
  
  svg {
    width: 20px;
    height: 20px;
  }
`;

const ProgressContainer = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const ProgressBar = styled.div`
  position: relative;
  /* Larger touch target on mobile */
  height: 8px;
  background: ${({ theme }) => theme.colors.gray[200]};
  border-radius: ${({ theme }) => theme.borderRadius.full};
  cursor: pointer;
  overflow: hidden;
  /* Add padding for easier touch interaction */
  padding: 4px 0;
  margin: -4px 0;
  
  /* Smaller on desktop */
  ${({ theme }) => theme.mediaQueries.desktop} {
    height: 4px;
    padding: 2px 0;
    margin: -2px 0;
  }
  
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 4px;
    transform: translateY(-50%);
    background: ${({ theme }) => theme.colors.gray[200]};
    border-radius: ${({ theme }) => theme.borderRadius.full};
    
    ${({ theme }) => theme.mediaQueries.desktop} {
      height: 4px;
    }
  }
`;

const ProgressFill = styled.div<{ $progress: number }>`
  position: absolute;
  top: 50%;
  left: 0;
  height: 4px;
  width: ${({ $progress }) => $progress}%;
  background: ${({ theme }) => theme.colors.trustBlue};
  border-radius: ${({ theme }) => theme.borderRadius.full};
  transform: translateY(-50%);
  transition: width ${({ theme }) => theme.animations.duration.fast} 
    ${({ theme }) => theme.animations.easing.easeOut};
  z-index: 1;
`;

const TimeDisplay = styled.div`
  display: flex;
  justify-content: space-between;
  font-family: ${({ theme }) => theme.typography.fonts.english};
  font-size: ${({ theme }) => theme.typography.fontSizes.xs};
  color: ${({ theme }) => theme.colors.gray[600]};
`;

const VolumeContainer = styled.div`
  display: none;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  
  ${({ theme }) => theme.mediaQueries.tablet} {
    display: flex;
  }
`;

const VolumeSlider = styled.input`
  width: 80px;
  height: 6px;
  background: ${({ theme }) => theme.colors.gray[200]};
  border-radius: ${({ theme }) => theme.borderRadius.full};
  outline: none;
  cursor: pointer;
  /* Add padding for easier touch interaction */
  padding: 4px 0;
  margin: -4px 0;
  
  &::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    background: ${({ theme }) => theme.colors.trustBlue};
    border-radius: 50%;
    cursor: pointer;
    box-shadow: ${({ theme }) => theme.shadows.sm};
    
    /* Smaller on desktop */
    ${({ theme }) => theme.mediaQueries.desktop} {
      width: 16px;
      height: 16px;
    }
  }
  
  &::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: ${({ theme }) => theme.colors.trustBlue};
    border-radius: 50%;
    border: none;
    cursor: pointer;
    box-shadow: ${({ theme }) => theme.shadows.sm};
    
    /* Smaller on desktop */
    ${({ theme }) => theme.mediaQueries.desktop} {
      width: 16px;
      height: 16px;
    }
  }
  
  /* Focus styles for accessibility */
  &:focus {
    outline: 2px solid ${({ theme }) => theme.colors.trustBlue};
    outline-offset: 2px;
  }
`;

const LoadingSpinner = styled.div`
  width: 20px;
  height: 20px;
  border: 2px solid ${({ theme }) => theme.colors.gray[300]};
  border-top: 2px solid ${({ theme }) => theme.colors.trustBlue};
  border-radius: 50%;
  animation: spin 1s linear infinite;
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const ErrorMessage = styled.div`
  background: #fee;
  color: #c53030;
  padding: ${({ theme }) => theme.spacing[2]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-size: ${({ theme }) => theme.typography.fontSizes.xs};
  text-align: center;
`;

const KeyboardHint = styled.div`
  display: none;
  font-family: ${({ theme }) => theme.typography.fonts.english};
  font-size: ${({ theme }) => theme.typography.fontSizes.xs};
  color: ${({ theme }) => theme.colors.gray[500]};
  text-align: center;
  margin-top: ${({ theme }) => theme.spacing[2]};
  
  ${({ theme }) => theme.mediaQueries.desktop} {
    display: block;
  }
`;

// Play/Pause icons
const PlayIcon = () => (
  <svg viewBox="0 0 24 24" fill="currentColor">
    <path d="M8 5v14l11-7z"/>
  </svg>
);

const PauseIcon = () => (
  <svg viewBox="0 0 24 24" fill="currentColor">
    <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
  </svg>
);

const VolumeIcon = () => (
  <svg viewBox="0 0 24 24" fill="currentColor" width="16" height="16">
    <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
  </svg>
);

const PreviousIcon = () => (
  <svg viewBox="0 0 24 24" fill="currentColor">
    <path d="M6 6h2v12H6zm3.5 6l8.5 6V6z"/>
  </svg>
);

const NextIcon = () => (
  <svg viewBox="0 0 24 24" fill="currentColor">
    <path d="M6 18l8.5-6L6 6v12zM16 6v12h2V6h-2z"/>
  </svg>
);

const PlaylistIcon = () => (
  <svg viewBox="0 0 24 24" fill="currentColor" width="16" height="16">
    <path d="M15 6H3v2h12V6zm0 4H3v2h12v-2zM3 16h8v-2H3v2zM17 6v8.18c-.31-.11-.65-.18-1-.18-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3V8h3V6h-5z"/>
  </svg>
);

export const AudioPlayer: React.FC = () => {
  const { 
    state, 
    pauseAudio, 
    resumeAudio, 
    seekTo, 
    setVolume,
    nextLesson,
    previousLesson
  } = useAudio();

  const { } = useScreenReaderAnnouncement();
  const { announceChange, registerShortcut, unregisterShortcut } = useAccessibility();
  const playerRef = useRef<HTMLDivElement>(null);
  const isVisible = state.currentLesson !== null;
  const progress = calculateProgress(state.currentTime, state.duration);
  
  // Enhanced screen reader announcements for audio state changes
  useEffect(() => {
    if (state.currentLesson) {
      if (state.isPlaying) {
        const progressText = state.duration > 0 
          ? ` (${formatTime(state.currentTime)} / ${formatTime(state.duration)})` 
          : '';
        announceChange(`正在播放: ${state.currentLesson.title}${progressText}`, 'polite');
      } else if (!state.isLoading) {
        announceChange(`已暂停: ${state.currentLesson.title}`, 'polite');
      }
    }
  }, [state.isPlaying, state.currentLesson, state.isLoading, announceChange]);

  useEffect(() => {
    if (state.error) {
      announceChange(`播放错误: ${state.error}. 请尝试重新播放或选择其他课程`, 'assertive');
    }
  }, [state.error, announceChange]);

  useEffect(() => {
    if (state.currentLesson && !state.isLoading && !state.error) {
      const playlistInfo = state.playlist.length > 1 
        ? ` (第${state.currentIndex + 1}课，共${state.playlist.length}课)` 
        : '';
      announceChange(`已加载课程: ${state.currentLesson.title}${playlistInfo}`, 'polite');
    }
  }, [state.currentLesson, state.isLoading, state.error, state.currentIndex, state.playlist.length, announceChange]);

  // Announce progress at intervals for long content
  useEffect(() => {
    if (!state.isPlaying || !state.currentLesson || state.duration === 0) return;

    const interval = setInterval(() => {
      const progressPercent = Math.round((state.currentTime / state.duration) * 100);
      if (progressPercent > 0 && progressPercent % 25 === 0) {
        announceChange(`播放进度: ${progressPercent}%`, 'polite');
      }
    }, 5000);

    return () => clearInterval(interval);
  }, [state.isPlaying, state.currentTime, state.duration, state.currentLesson, announceChange]);

  // Enhanced keyboard shortcuts with accessibility
  useEffect(() => {
    if (!isVisible) return;

    // Register keyboard shortcuts
    registerShortcut({
      key: ' ',
      action: () => {
        if (state.isPlaying) {
          pauseAudio();
          announceChange('音频已暂停', 'polite');
        } else {
          resumeAudio();
          announceChange('音频开始播放', 'polite');
        }
      },
      description: '播放/暂停'
    });

    registerShortcut({
      key: 'ArrowLeft',
      action: () => {
        const newTime = Math.max(0, state.currentTime - 10);
        seekTo(newTime);
        announceChange(`快退到 ${formatTime(newTime)}`, 'polite');
      },
      description: '快退10秒'
    });

    registerShortcut({
      key: 'ArrowRight',
      action: () => {
        const newTime = Math.min(state.duration, state.currentTime + 10);
        seekTo(newTime);
        announceChange(`快进到 ${formatTime(newTime)}`, 'polite');
      },
      description: '快进10秒'
    });

    registerShortcut({
      key: 'ArrowLeft',
      shiftKey: true,
      action: () => {
        previousLesson();
        announceChange('切换到上一课', 'polite');
      },
      description: '上一课'
    });

    registerShortcut({
      key: 'ArrowRight',
      shiftKey: true,
      action: () => {
        nextLesson();
        announceChange('切换到下一课', 'polite');
      },
      description: '下一课'
    });

    registerShortcut({
      key: 'ArrowUp',
      action: () => {
        const newVolume = Math.min(1, state.volume + 0.1);
        setVolume(newVolume);
        announceChange(`音量调整到 ${Math.round(newVolume * 100)}%`, 'polite');
      },
      description: '音量增加'
    });

    registerShortcut({
      key: 'ArrowDown',
      action: () => {
        const newVolume = Math.max(0, state.volume - 0.1);
        setVolume(newVolume);
        announceChange(`音量调整到 ${Math.round(newVolume * 100)}%`, 'polite');
      },
      description: '音量减少'
    });

    registerShortcut({
      key: 'h',
      action: () => {
        announceChange('音频播放器快捷键: 空格键播放暂停, 左右箭头快进快退, Shift+左右箭头切换课程, 上下箭头调节音量', 'polite');
      },
      description: '显示帮助'
    });

    // Cleanup shortcuts when component unmounts or becomes invisible
    return () => {
      unregisterShortcut(' ');
      unregisterShortcut('ArrowLeft');
      unregisterShortcut('ArrowRight');
      unregisterShortcut('ArrowUp');
      unregisterShortcut('ArrowDown');
      unregisterShortcut('h');
    };
  }, [isVisible, state.isPlaying, state.currentTime, state.duration, state.volume, 
      pauseAudio, resumeAudio, seekTo, setVolume, nextLesson, previousLesson, 
      announceChange, registerShortcut, unregisterShortcut]);

  const handlePlayPause = () => {
    if (state.isPlaying) {
      pauseAudio();
    } else {
      resumeAudio();
    }
  };

  const handleProgressClick = (event: React.MouseEvent<HTMLDivElement>) => {
    const rect = event.currentTarget.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const percentage = clickX / rect.width;
    const newTime = percentage * state.duration;
    seekTo(newTime);
  };

  const handleVolumeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(event.target.value);
    setVolume(newVolume);
  };

  const handlePreviousLesson = () => {
    previousLesson();
  };

  const handleNextLesson = () => {
    nextLesson();
  };

  const canGoPrevious = state.currentIndex > 0;
  const canGoNext = state.currentIndex < state.playlist.length - 1;

  if (!isVisible) {
    return null;
  }

  return (
    <PlayerContainer 
      ref={playerRef}
      $isVisible={isVisible}
      role="region"
      aria-label="音频播放器"
      aria-live="polite"
      aria-describedby="audio-help-text"
    >
      <PlayerContent>
        {state.error && (
          <ErrorMessage role="alert" aria-live="assertive">
            {state.error}
          </ErrorMessage>
        )}
        
        <LessonInfo>
          <LessonTitle>{state.currentLesson?.title}</LessonTitle>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <LessonDuration>
              课程时长: {formatTime(state.currentLesson?.duration || 0)}
            </LessonDuration>
            {state.playlist.length > 0 && (
              <PlaylistInfo>
                <PlaylistIcon />
                <CurrentLessonIndicator />
                <span>{state.currentIndex + 1} / {state.playlist.length}</span>
              </PlaylistInfo>
            )}
          </div>
        </LessonInfo>

        <ControlsContainer role="group" aria-label="音频控制">
          <NavigationButton
            onClick={handlePreviousLesson}
            disabled={!canGoPrevious || state.isLoading}
            title="上一课 (Shift + ←)"
            aria-label={`上一课${!canGoPrevious ? ' (不可用)' : ''}`}
            aria-disabled={!canGoPrevious || state.isLoading}
          >
            <PreviousIcon />
          </NavigationButton>

          <PlayButton 
            $isPlaying={state.isPlaying}
            onClick={handlePlayPause}
            disabled={state.isLoading}
            title={state.isPlaying ? "暂停 (空格)" : "播放 (空格)"}
            aria-label={
              state.isLoading 
                ? "正在加载..." 
                : state.isPlaying 
                  ? `暂停播放 ${state.currentLesson?.title || ''}` 
                  : `播放 ${state.currentLesson?.title || ''}`
            }
            aria-pressed={state.isPlaying}
          >
            {state.isLoading ? (
              <LoadingSpinner />
            ) : state.isPlaying ? (
              <PauseIcon />
            ) : (
              <PlayIcon />
            )}
          </PlayButton>

          <NavigationButton
            onClick={handleNextLesson}
            disabled={!canGoNext || state.isLoading}
            title="下一课 (Shift + →)"
            aria-label={`下一课${!canGoNext ? ' (不可用)' : ''}`}
            aria-disabled={!canGoNext || state.isLoading}
          >
            <NextIcon />
          </NavigationButton>

          <ProgressContainer>
            <ProgressBar 
              onClick={handleProgressClick}
              role="slider"
              aria-label="播放进度"
              aria-valuemin={0}
              aria-valuemax={state.duration}
              aria-valuenow={state.currentTime}
              aria-valuetext={`${formatTime(state.currentTime)} / ${formatTime(state.duration)}`}
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === 'ArrowLeft') {
                  e.preventDefault();
                  seekTo(Math.max(0, state.currentTime - 10));
                } else if (e.key === 'ArrowRight') {
                  e.preventDefault();
                  seekTo(Math.min(state.duration, state.currentTime + 10));
                }
              }}
            >
              <ProgressFill $progress={progress} />
            </ProgressBar>
            <TimeDisplay aria-label="播放时间">
              <span aria-label="当前时间">{formatTime(state.currentTime)}</span>
              <span aria-label="总时长">{formatTime(state.duration)}</span>
            </TimeDisplay>
          </ProgressContainer>

          <VolumeContainer>
            <VolumeIcon />
            <VolumeSlider
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={state.volume}
              onChange={handleVolumeChange}
              aria-label="音量控制"
              aria-valuetext={`音量 ${Math.round(state.volume * 100)}%`}
            />
          </VolumeContainer>
        </ControlsContainer>

        <KeyboardHint id="audio-help-text" role="note" aria-label="键盘快捷键说明">
          快捷键: 空格=播放/暂停 | ←→=快进/快退 | Shift+←→=上/下一课 | ↑↓=音量 | H=帮助
        </KeyboardHint>
      </PlayerContent>
    </PlayerContainer>
  );
};