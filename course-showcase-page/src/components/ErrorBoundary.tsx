import React, { Component } from 'react';
import type { ErrorInfo, ReactNode } from 'react';
import styled from 'styled-components';
import { Typo<PERSON>, <PERSON><PERSON>, Card } from './ui';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

const ErrorContainer = styled(Card)`
  max-width: 600px;
  margin: ${({ theme }) => theme.spacing[8]} auto;
  text-align: center;
`;

const ErrorIcon = styled.div`
  font-size: 48px;
  color: ${({ theme }) => theme.colors.actionOrange};
  margin-bottom: ${({ theme }) => theme.spacing[4]};
`;

const ErrorTitle = styled(Typography)`
  color: ${({ theme }) => theme.colors.gray[800]};
  margin-bottom: ${({ theme }) => theme.spacing[3]};
`;

const ErrorMessage = styled(Typography)`
  color: ${({ theme }) => theme.colors.gray[600]};
  margin-bottom: ${({ theme }) => theme.spacing[6]};
  line-height: ${({ theme }) => theme.typography.lineHeights.relaxed};
`;

const ErrorActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[3]};
  justify-content: center;
  flex-wrap: wrap;
`;

const ErrorDetails = styled.details`
  margin-top: ${({ theme }) => theme.spacing[6]};
  text-align: left;
  
  summary {
    cursor: pointer;
    color: ${({ theme }) => theme.colors.trustBlue};
    font-weight: ${({ theme }) => theme.typography.fontWeights.medium};
    margin-bottom: ${({ theme }) => theme.spacing[2]};
    
    &:hover {
      text-decoration: underline;
    }
  }
`;

const ErrorStack = styled.pre`
  background: ${({ theme }) => theme.colors.gray[50]};
  border: 1px solid ${({ theme }) => theme.colors.gray[200]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing[3]};
  font-size: ${({ theme }) => theme.typography.fontSizes.xs};
  color: ${({ theme }) => theme.colors.gray[700]};
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;
`;

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo
    });

    // 调用自定义错误处理函数
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // 在开发环境中记录错误
    if (process.env.NODE_ENV === 'development') {
      console.error('ErrorBoundary caught an error:', error, errorInfo);
    }

    // 在生产环境中可以发送错误到监控服务
    if (process.env.NODE_ENV === 'production') {
      // 这里可以集成错误监控服务，如 Sentry
      console.error('Production error:', {
        error: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack
      });
    }
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });
  };

  handleReload = () => {
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义fallback，使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // 默认错误UI
      return (
        <ErrorContainer variant="elevated" padding="lg">
          <ErrorIcon role="img" aria-label="错误图标">
            ⚠️
          </ErrorIcon>
          
          <ErrorTitle variant="h3" chinese weight="bold">
            页面出现了问题
          </ErrorTitle>
          
          <ErrorMessage variant="body" chinese>
            很抱歉，页面遇到了意外错误。您可以尝试刷新页面或重新加载组件。
            如果问题持续存在，请联系技术支持。
          </ErrorMessage>
          
          <ErrorActions>
            <Button
              variant="primary"
              onClick={this.handleRetry}
              aria-label="重试加载组件"
            >
              重试
            </Button>
            
            <Button
              variant="secondary"
              onClick={this.handleReload}
              aria-label="刷新整个页面"
            >
              刷新页面
            </Button>
          </ErrorActions>
          
          {process.env.NODE_ENV === 'development' && this.state.error && (
            <ErrorDetails>
              <summary>查看错误详情（开发模式）</summary>
              <ErrorStack>
                <strong>错误信息:</strong> {this.state.error.message}
                {'\n\n'}
                <strong>错误堆栈:</strong>
                {'\n'}
                {this.state.error.stack}
                {this.state.errorInfo && (
                  <>
                    {'\n\n'}
                    <strong>组件堆栈:</strong>
                    {'\n'}
                    {this.state.errorInfo.componentStack}
                  </>
                )}
              </ErrorStack>
            </ErrorDetails>
          )}
        </ErrorContainer>
      );
    }

    return this.props.children;
  }
}

// 高阶组件，用于包装其他组件
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode,
  onError?: (error: Error, errorInfo: ErrorInfo) => void
) => {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary fallback={fallback} onError={onError}>
      <Component {...props} />
    </ErrorBoundary>
  );
  
  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

export default ErrorBoundary;