import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { useAccessibility } from '../hooks';
import { generateAccessibilityReport, logAccessibilityIssues } from '../utils/accessibilityValidation';

interface AccessibilityValidatorProps {
  enabled?: boolean;
  autoAudit?: boolean;
  showReport?: boolean;
}

const ValidatorContainer = styled.div<{ $isVisible: boolean }>`
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  background: ${({ theme }) => theme.colors.surface};
  border: 2px solid ${({ theme }) => theme.colors.trustBlue};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  padding: ${({ theme }) => theme.spacing[4]};
  box-shadow: ${({ theme }) => theme.shadows.lg};
  max-width: 400px;
  max-height: 500px;
  overflow-y: auto;
  transform: ${({ $isVisible }) => $isVisible ? 'translateX(0)' : 'translateX(100%)'};
  transition: transform ${({ theme }) => theme.animations.duration.normal} ${({ theme }) => theme.animations.easing.easeOut};
  
  /* Hide in production */
  display: ${process.env.NODE_ENV === 'development' ? 'block' : 'none'};
`;

const ValidatorHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing[4]};
  padding-bottom: ${({ theme }) => theme.spacing[2]};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
`;

const ValidatorTitle = styled.h3`
  font-family: ${({ theme }) => theme.typography.fonts.english};
  font-size: ${({ theme }) => theme.typography.fontSizes.lg};
  font-weight: ${({ theme }) => theme.typography.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.trustBlue};
  margin: 0;
`;

const CloseButton = styled.button`
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: ${({ theme }) => theme.colors.gray[200]};
  color: ${({ theme }) => theme.colors.gray[600]};
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all ${({ theme }) => theme.animations.duration.normal} ${({ theme }) => theme.animations.easing.easeOut};
  
  &:hover {
    background: ${({ theme }) => theme.colors.gray[300]};
    color: ${({ theme }) => theme.colors.gray[800]};
  }
  
  &:focus {
    outline: 2px solid ${({ theme }) => theme.colors.trustBlue};
    outline-offset: 2px;
  }
`;

const ScoreDisplay = styled.div<{ $score: number }>`
  text-align: center;
  padding: ${({ theme }) => theme.spacing[3]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  margin-bottom: ${({ theme }) => theme.spacing[4]};
  
  ${({ $score, theme }) => {
    if ($score >= 90) {
      return `
        background: ${theme.colors.growthGreen}20;
        border: 1px solid ${theme.colors.growthGreen};
        color: ${theme.colors.growthGreen};
      `;
    } else if ($score >= 70) {
      return `
        background: ${theme.colors.actionOrange}20;
        border: 1px solid ${theme.colors.actionOrange};
        color: ${theme.colors.actionOrange};
      `;
    } else {
      return `
        background: #fee;
        border: 1px solid #ef4444;
        color: #ef4444;
      `;
    }
  }}
`;

const ScoreNumber = styled.div`
  font-size: ${({ theme }) => theme.typography.fontSizes['2xl']};
  font-weight: ${({ theme }) => theme.typography.fontWeights.bold};
  margin-bottom: ${({ theme }) => theme.spacing[1]};
`;

const ScoreLabel = styled.div`
  font-size: ${({ theme }) => theme.typography.fontSizes.sm};
  font-weight: ${({ theme }) => theme.typography.fontWeights.medium};
`;

const IssuesList = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const IssueItem = styled.div<{ $severity: 'error' | 'warning' | 'info' }>`
  padding: ${({ theme }) => theme.spacing[3]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  border-left: 4px solid;
  
  ${({ $severity, theme }) => {
    switch ($severity) {
      case 'error':
        return `
          background: #fef2f2;
          border-left-color: #ef4444;
          color: #991b1b;
        `;
      case 'warning':
        return `
          background: #fffbeb;
          border-left-color: #f59e0b;
          color: #92400e;
        `;
      case 'info':
        return `
          background: #eff6ff;
          border-left-color: ${theme.colors.trustBlue};
          color: #1e40af;
        `;
      default:
        return '';
    }
  }}
`;

const IssueHeader = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  margin-bottom: ${({ theme }) => theme.spacing[1]};
`;

const IssueIcon = styled.span<{ $severity: 'error' | 'warning' | 'info' }>`
  font-size: 16px;
  
  &::before {
    content: ${({ $severity }) => {
      switch ($severity) {
        case 'error': return '"❌"';
        case 'warning': return '"⚠️"';
        case 'info': return '"ℹ️"';
        default: return '""';
      }
    }};
  }
`;

const IssueMessage = styled.div`
  font-size: ${({ theme }) => theme.typography.fontSizes.sm};
  font-weight: ${({ theme }) => theme.typography.fontWeights.medium};
`;

const IssueSuggestion = styled.div`
  font-size: ${({ theme }) => theme.typography.fontSizes.xs};
  opacity: 0.8;
  margin-top: ${({ theme }) => theme.spacing[1]};
`;

const ActionButtons = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[2]};
  margin-top: ${({ theme }) => theme.spacing[4]};
  padding-top: ${({ theme }) => theme.spacing[4]};
  border-top: 1px solid ${({ theme }) => theme.colors.border};
`;

const ActionButton = styled.button<{ $variant: 'primary' | 'secondary' }>`
  flex: 1;
  padding: ${({ theme }) => theme.spacing[2]} ${({ theme }) => theme.spacing[3]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-size: ${({ theme }) => theme.typography.fontSizes.sm};
  font-weight: ${({ theme }) => theme.typography.fontWeights.medium};
  cursor: pointer;
  transition: all ${({ theme }) => theme.animations.duration.normal} ${({ theme }) => theme.animations.easing.easeOut};
  
  ${({ $variant, theme }) => {
    if ($variant === 'primary') {
      return `
        background: ${theme.colors.trustBlue};
        color: ${theme.colors.white};
        border: 1px solid ${theme.colors.trustBlue};
        
        &:hover {
          background: ${theme.colors.trustBlue}dd;
        }
      `;
    } else {
      return `
        background: transparent;
        color: ${theme.colors.trustBlue};
        border: 1px solid ${theme.colors.trustBlue};
        
        &:hover {
          background: ${theme.colors.trustBlue}10;
        }
      `;
    }
  }}
  
  &:focus {
    outline: 2px solid ${({ theme }) => theme.colors.trustBlue};
    outline-offset: 2px;
  }
`;

const ToggleButton = styled.button`
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: ${({ theme }) => theme.colors.trustBlue};
  color: ${({ theme }) => theme.colors.white};
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  cursor: pointer;
  box-shadow: ${({ theme }) => theme.shadows.lg};
  transition: all ${({ theme }) => theme.animations.duration.normal} ${({ theme }) => theme.animations.easing.easeOut};
  
  /* Hide in production */
  display: ${process.env.NODE_ENV === 'development' ? 'flex' : 'none'};
  
  &:hover {
    transform: scale(1.05);
    box-shadow: ${({ theme }) => theme.shadows.xl};
  }
  
  &:focus {
    outline: 2px solid ${({ theme }) => theme.colors.actionOrange};
    outline-offset: 2px;
  }
`;

export const AccessibilityValidator: React.FC<AccessibilityValidatorProps> = ({
  enabled = process.env.NODE_ENV === 'development',
  autoAudit = true,
  showReport = false,
}) => {
  const [isVisible, setIsVisible] = useState(showReport);
  const [report, setReport] = useState<ReturnType<typeof generateAccessibilityReport> | null>(null);
  const [isAuditing, setIsAuditing] = useState(false);
  const { announceChange } = useAccessibility();

  // Run accessibility audit
  const runAudit = async () => {
    if (!enabled) return;
    
    setIsAuditing(true);
    announceChange('开始无障碍审计...', 'polite');
    
    try {
      // Small delay to allow UI updates
      await new Promise(resolve => setTimeout(resolve, 100));
      
      const auditReport = generateAccessibilityReport();
      setReport(auditReport);
      
      // Log to console for developers
      logAccessibilityIssues();
      
      announceChange(
        `无障碍审计完成。得分: ${auditReport.summary.score}/100, 发现 ${auditReport.summary.total} 个问题`,
        'polite'
      );
    } catch (error) {
      console.error('Accessibility audit failed:', error);
      announceChange('无障碍审计失败', 'assertive');
    } finally {
      setIsAuditing(false);
    }
  };

  // Auto-audit on mount and when DOM changes
  useEffect(() => {
    if (!enabled || !autoAudit) return;

    // Initial audit
    const initialAuditTimer = setTimeout(runAudit, 1000);

    // Set up mutation observer for DOM changes
    const observer = new MutationObserver(() => {
      // Debounce audit calls
      clearTimeout(initialAuditTimer);
      setTimeout(runAudit, 500);
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['class', 'style', 'aria-label', 'aria-labelledby', 'role']
    });

    return () => {
      clearTimeout(initialAuditTimer);
      observer.disconnect();
    };
  }, [enabled, autoAudit]);

  // Keyboard shortcut to toggle validator
  useEffect(() => {
    if (!enabled) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + Shift + A to toggle accessibility validator
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'A') {
        event.preventDefault();
        setIsVisible(prev => !prev);
        announceChange(isVisible ? '关闭无障碍验证器' : '打开无障碍验证器', 'polite');
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [enabled, isVisible, announceChange]);

  if (!enabled) return null;

  const toggleVisibility = () => {
    setIsVisible(prev => !prev);
    announceChange(isVisible ? '关闭无障碍验证器' : '打开无障碍验证器', 'polite');
  };

  const handleClose = () => {
    setIsVisible(false);
    announceChange('关闭无障碍验证器', 'polite');
  };

  return (
    <>
      {!isVisible && (
        <ToggleButton
          onClick={toggleVisibility}
          aria-label="打开无障碍验证器 (Ctrl+Shift+A)"
          title="无障碍验证器"
        >
          🔍
        </ToggleButton>
      )}
      
      <ValidatorContainer
        $isVisible={isVisible}
        role="dialog"
        aria-label="无障碍验证器"
        aria-modal="false"
      >
        <ValidatorHeader>
          <ValidatorTitle>无障碍验证器</ValidatorTitle>
          <CloseButton
            onClick={handleClose}
            aria-label="关闭验证器"
          >
            ×
          </CloseButton>
        </ValidatorHeader>

        {report && (
          <>
            <ScoreDisplay $score={report.summary.score}>
              <ScoreNumber>{report.summary.score}</ScoreNumber>
              <ScoreLabel>无障碍得分 / 100</ScoreLabel>
            </ScoreDisplay>

            {report.issues.length > 0 && (
              <IssuesList>
                {report.issues.slice(0, 10).map((issue, index) => (
                  <IssueItem key={index} $severity={issue.severity}>
                    <IssueHeader>
                      <IssueIcon $severity={issue.severity} />
                      <IssueMessage>{issue.message}</IssueMessage>
                    </IssueHeader>
                    <IssueSuggestion>{issue.suggestion}</IssueSuggestion>
                  </IssueItem>
                ))}
                {report.issues.length > 10 && (
                  <div style={{ textAlign: 'center', fontSize: '14px', color: '#666' }}>
                    还有 {report.issues.length - 10} 个问题...
                  </div>
                )}
              </IssuesList>
            )}
          </>
        )}

        <ActionButtons>
          <ActionButton
            $variant="primary"
            onClick={runAudit}
            disabled={isAuditing}
            aria-label={isAuditing ? '正在审计...' : '重新审计'}
          >
            {isAuditing ? '审计中...' : '重新审计'}
          </ActionButton>
          <ActionButton
            $variant="secondary"
            onClick={() => console.log('Full report:', report)}
            aria-label="在控制台查看完整报告"
          >
            查看详情
          </ActionButton>
        </ActionButtons>
      </ValidatorContainer>
    </>
  );
};

export default AccessibilityValidator;