import React, { useState, useEffect } from 'react';
import styled from 'styled-components';

interface ViewportInfo {
  width: number;
  height: number;
  deviceType: 'mobile' | 'tablet' | 'desktop';
  orientation: 'portrait' | 'landscape';
  pixelRatio: number;
}

const TestPanel = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== 'isVisible'
})<{ isVisible: boolean }>`
  position: fixed;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: ${({ theme }) => theme.spacing[4]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-family: monospace;
  font-size: 12px;
  z-index: 10000;
  min-width: 200px;
  transform: ${({ isVisible }) => isVisible ? 'translateX(0)' : 'translateX(100%)'};
  transition: transform 0.3s ease;
  
  @media (max-width: 480px) {
    top: 5px;
    right: 5px;
    font-size: 10px;
    min-width: 150px;
    padding: ${({ theme }) => theme.spacing[2]};
  }
`;

const ToggleButton = styled.button`
  position: fixed;
  top: 10px;
  right: 10px;
  background: ${({ theme }) => theme.colors.trustBlue};
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  cursor: pointer;
  font-size: 12px;
  z-index: 10001;
  
  &:hover {
    background: ${({ theme }) => theme.colors.actionOrange};
  }
  
  @media (max-width: 480px) {
    top: 5px;
    right: 5px;
    padding: 6px 10px;
    font-size: 10px;
  }
`;

const InfoRow = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const Label = styled.span`
  color: #ccc;
`;

const Value = styled.span`
  color: #fff;
  font-weight: bold;
`;

const BreakpointIndicator = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== 'deviceType'
})<{ deviceType: string }>`
  margin-top: 8px;
  padding: 4px 8px;
  border-radius: 4px;
  text-align: center;
  font-weight: bold;
  background: ${({ deviceType }) => {
    switch (deviceType) {
      case 'mobile': return '#e74c3c';
      case 'tablet': return '#f39c12';
      case 'desktop': return '#27ae60';
      default: return '#95a5a6';
    }
  }};
`;

const TestGrid = styled.div`
  margin-top: 12px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4px;
  font-size: 10px;
`;

const TestItem = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== 'passed'
})<{ passed: boolean }>`
  padding: 2px 4px;
  border-radius: 2px;
  background: ${({ passed }) => passed ? '#27ae60' : '#e74c3c'};
  text-align: center;
`;

interface ResponsiveTestHelperProps {
  showByDefault?: boolean;
}

export const ResponsiveTestHelper: React.FC<ResponsiveTestHelperProps> = ({ 
  showByDefault = false 
}) => {
  const [isVisible, setIsVisible] = useState(showByDefault);
  const [viewportInfo, setViewportInfo] = useState<ViewportInfo>({
    width: 0,
    height: 0,
    deviceType: 'desktop',
    orientation: 'landscape',
    pixelRatio: 1
  });

  const updateViewportInfo = () => {
    const width = window.innerWidth;
    const height = window.innerHeight;
    
    let deviceType: 'mobile' | 'tablet' | 'desktop' = 'desktop';
    if (width < 768) {
      deviceType = 'mobile';
    } else if (width < 1024) {
      deviceType = 'tablet';
    }

    const orientation = width > height ? 'landscape' : 'portrait';
    const pixelRatio = window.devicePixelRatio || 1;

    setViewportInfo({
      width,
      height,
      deviceType,
      orientation,
      pixelRatio
    });
  };

  useEffect(() => {
    updateViewportInfo();
    
    const handleResize = () => {
      updateViewportInfo();
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 响应式测试项目
  const responsiveTests = [
    {
      name: 'Touch',
      passed: 'ontouchstart' in window
    },
    {
      name: 'Flexbox',
      passed: CSS.supports('display', 'flex')
    },
    {
      name: 'Grid',
      passed: CSS.supports('display', 'grid')
    },
    {
      name: 'CSS Vars',
      passed: CSS.supports('color', 'var(--test)')
    },
    {
      name: 'Media Q',
      passed: typeof window.matchMedia !== 'undefined'
    },
    {
      name: 'Viewport',
      passed: !!document.querySelector('meta[name="viewport"]')
    }
  ];

  // 仅在开发环境显示
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <>
      <ToggleButton
        onClick={() => setIsVisible(!isVisible)}
        aria-label={isVisible ? '隐藏响应式测试面板' : '显示响应式测试面板'}
      >
        {isVisible ? '隐藏' : '测试'}
      </ToggleButton>
      
      <TestPanel isVisible={isVisible}>
        <InfoRow>
          <Label>尺寸:</Label>
          <Value>{viewportInfo.width} × {viewportInfo.height}</Value>
        </InfoRow>
        
        <InfoRow>
          <Label>方向:</Label>
          <Value>{viewportInfo.orientation}</Value>
        </InfoRow>
        
        <InfoRow>
          <Label>像素比:</Label>
          <Value>{viewportInfo.pixelRatio}x</Value>
        </InfoRow>
        
        <BreakpointIndicator deviceType={viewportInfo.deviceType}>
          {viewportInfo.deviceType.toUpperCase()}
        </BreakpointIndicator>
        
        <TestGrid>
          {responsiveTests.map((test, index) => (
            <TestItem key={index} passed={test.passed}>
              {test.name}
            </TestItem>
          ))}
        </TestGrid>
      </TestPanel>
    </>
  );
};

export default ResponsiveTestHelper;