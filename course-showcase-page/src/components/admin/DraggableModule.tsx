import React from 'react';
import styled from 'styled-components';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Typography, Button } from '../ui';
import { DraggableLessonList } from './DraggableLessonList';
import type { CourseModule, Lesson } from '../../types';

interface DraggableModuleProps {
  module: CourseModule;
  onEditModule: (module: CourseModule) => void;
  onDeleteModule: (moduleId: string) => void;
  onAddLesson: (moduleId: string) => void;
  onEditLesson: (moduleId: string, lesson: Lesson) => void;
  onDeleteLesson: (moduleId: string, lessonId: string) => void;
  onReorderLessons: (moduleId: string, lessonIds: string[]) => void;
}

const ModuleCard = styled.div<{ $isDragging: boolean }>`
  background: ${({ theme }) => theme.colors.white};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  padding: ${({ theme }) => theme.spacing[6]};
  box-shadow: ${({ theme }) => theme.shadows.base};
  border: 2px solid transparent;
  transition: all ${({ theme }) => theme.animations.duration.normal} ${({ theme }) => theme.animations.easing.easeOut};
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: ${({ theme }) => theme.shadows.lg};
  }
  
  ${({ $isDragging }) => $isDragging && `
    transform: rotate(2deg);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    border-color: #f97316;
    z-index: 1000;
  `}
`;

const ModuleHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing[4]};
  gap: ${({ theme }) => theme.spacing[4]};
`;

const ModuleInfo = styled.div`
  flex: 1;
`;

const ModuleActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const DragHandle = styled.div<{ $isDragging: boolean }>`
  cursor: grab;
  padding: ${({ theme }) => theme.spacing[2]};
  color: ${({ theme }) => theme.colors.gray[400]};
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  transition: all ${({ theme }) => theme.animations.duration.fast} ${({ theme }) => theme.animations.easing.easeOut};
  
  &:hover {
    color: ${({ theme }) => theme.colors.gray[600]};
    background: ${({ theme }) => theme.colors.gray[100]};
  }
  
  &:active {
    cursor: grabbing;
  }
  
  ${({ $isDragging }) => $isDragging && `
    cursor: grabbing;
    color: #f97316;
  `}
`;

const DeleteButton = styled(Button)`
  color: #ef4444;
  border-color: #ef4444;
  
  &:hover {
    background-color: #ef4444;
    color: white;
  }
`;

const LessonsList = styled.div`
  margin-top: ${({ theme }) => theme.spacing[4]};
  padding-top: ${({ theme }) => theme.spacing[4]};
  border-top: 1px solid ${({ theme }) => theme.colors.gray[200]};
`;

const AddButton = styled(Button)`
  margin-top: ${({ theme }) => theme.spacing[4]};
`;

export const DraggableModule: React.FC<DraggableModuleProps> = ({
  module,
  onEditModule,
  onDeleteModule,
  onAddLesson,
  onEditLesson,
  onDeleteLesson,
  onReorderLessons
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: module.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <ModuleCard
      ref={setNodeRef}
      style={style}
      $isDragging={isDragging}
    >
      <ModuleHeader>
        <DragHandle
          {...attributes}
          {...listeners}
          $isDragging={isDragging}
          title="拖拽来重新排序模块"
        >
          ⋮⋮
        </DragHandle>
        <ModuleInfo>
          <Typography variant="h3" chinese weight="bold">
            {module.title}
          </Typography>
          <Typography variant="body" chinese color="#666">
            {module.description}
          </Typography>
        </ModuleInfo>
        <ModuleActions>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onEditModule(module)}
          >
            编辑
          </Button>
          <DeleteButton
            variant="outline"
            size="sm"
            onClick={() => onDeleteModule(module.id)}
          >
            删除
          </DeleteButton>
        </ModuleActions>
      </ModuleHeader>

      <LessonsList>
        <DraggableLessonList
          moduleId={module.id}
          lessons={module.lessons}
          onEditLesson={onEditLesson}
          onDeleteLesson={onDeleteLesson}
          onReorderLessons={onReorderLessons}
        />
        <AddButton
          variant="outline"
          onClick={() => onAddLesson(module.id)}
        >
          + 添加课程
        </AddButton>
      </LessonsList>
    </ModuleCard>
  );
};
