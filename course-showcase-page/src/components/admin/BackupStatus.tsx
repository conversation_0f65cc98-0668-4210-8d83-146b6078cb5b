import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { Typography } from '../ui';
import { useAuth } from '../../hooks/useAuth';

interface BackupStatusProps {
  token: string | null;
}

interface BackupStats {
  total: number;
  auto: number;
  manual: number;
  preRestore: number;
  totalSize: number;
  oldestBackup: any;
  newestBackup: any;
  isAutoBackupEnabled: boolean;
  autoBackupInterval: number;
}

const StatusContainer = styled.div`
  background: linear-gradient(135deg, #e0f2fe 0%, #f0f9ff 100%);
  border: 1px solid #0ea5e9;
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  padding: ${({ theme }) => theme.spacing[4]};
  margin-bottom: ${({ theme }) => theme.spacing[4]};
`;

const StatusGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: ${({ theme }) => theme.spacing[3]};
  margin-top: ${({ theme }) => theme.spacing[3]};
`;

const StatusItem = styled.div`
  text-align: center;
`;

const StatusValue = styled.div`
  font-size: ${({ theme }) => theme.typography.fontSizes.lg};
  font-weight: ${({ theme }) => theme.typography.fontWeights.bold};
  color: ${({ theme }) => theme.colors.trustBlue};
`;

const StatusLabel = styled.div`
  font-size: ${({ theme }) => theme.typography.fontSizes.sm};
  color: ${({ theme }) => theme.colors.gray[600]};
  margin-top: ${({ theme }) => theme.spacing[1]};
`;

const API_BASE = 'http://localhost:3001/api';

export const BackupStatus: React.FC<BackupStatusProps> = ({ token }) => {
  const { authenticatedFetch } = useAuth();
  const [stats, setStats] = useState<BackupStats | null>(null);
  const [loading, setLoading] = useState(true);

  const loadStats = async () => {
    if (!token) return;

    try {
      const response = await authenticatedFetch(`${API_BASE}/backups/stats`);
      const data = await response.json();

      if (data.success) {
        setStats(data.stats);
      } else {
        console.error('备份统计API返回错误:', data.error);
        setStats(null);
      }
    } catch (error) {
      console.error('加载备份统计失败:', error);
      setStats(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadStats();

    // 每30秒刷新一次统计
    const interval = setInterval(loadStats, 30000);
    return () => clearInterval(interval);
  }, [token, authenticatedFetch]);

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const formatDate = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <StatusContainer>
        <Typography variant="h6" chinese weight="medium" color="#0ea5e9">
          📊 备份状态
        </Typography>
        <Typography variant="caption" chinese color="#666" style={{ marginTop: '8px' }}>
          加载中...
        </Typography>
      </StatusContainer>
    );
  }

  if (!stats) {
    return (
      <StatusContainer>
        <Typography variant="h6" chinese weight="medium" color="#0ea5e9">
          📊 备份状态
        </Typography>
        <Typography variant="caption" chinese color="#ef4444" style={{ marginTop: '8px' }}>
          无法加载备份统计
        </Typography>
      </StatusContainer>
    );
  }

  return (
    <StatusContainer>
      <Typography variant="h6" chinese weight="medium" color="#0ea5e9">
        📊 备份状态
      </Typography>
      
      <StatusGrid>
        <StatusItem>
          <StatusValue>{stats.total}</StatusValue>
          <StatusLabel>总备份</StatusLabel>
        </StatusItem>
        
        <StatusItem>
          <StatusValue>{stats.auto}</StatusValue>
          <StatusLabel>自动备份</StatusLabel>
        </StatusItem>
        
        <StatusItem>
          <StatusValue>{stats.manual}</StatusValue>
          <StatusLabel>手动备份</StatusLabel>
        </StatusItem>
        
        <StatusItem>
          <StatusValue>{formatFileSize(stats.totalSize)}</StatusValue>
          <StatusLabel>总大小</StatusLabel>
        </StatusItem>
        
        {stats.newestBackup && (
          <StatusItem>
            <StatusValue>{formatDate(stats.newestBackup.timestamp)}</StatusValue>
            <StatusLabel>最新备份</StatusLabel>
          </StatusItem>
        )}
      </StatusGrid>
      
      <Typography variant="caption" chinese color="#666" style={{ marginTop: '12px', display: 'block' }}>
        {stats.isAutoBackupEnabled ? (
          `✅ 自动备份已启用，每${stats.autoBackupInterval}分钟运行一次`
        ) : (
          '⏸️ 自动备份已禁用，可在备份管理中启用'
        )}
        <br />
        💡 修改配置前会自动创建备份
      </Typography>
    </StatusContainer>
  );
};
