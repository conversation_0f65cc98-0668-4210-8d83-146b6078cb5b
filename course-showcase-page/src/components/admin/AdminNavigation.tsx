import React from 'react';
import styled from 'styled-components';
import { Typo<PERSON>, Button } from '../ui';

interface AdminNavigationProps {
  onPasswordManager: () => void;
  onBackupManager: () => void;
  onLogout: () => void;
}

const NavigationContainer = styled.div`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: ${({ theme }) => theme.spacing[4]} 0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  margin-bottom: ${({ theme }) => theme.spacing[6]};
`;

const NavigationContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 ${({ theme }) => theme.spacing[4]};
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: ${({ theme }) => theme.spacing[4]};
`;

const NavigationLeft = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[6]};
`;

const Logo = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
`;

const NavigationMenu = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[3]};
  flex-wrap: wrap;
`;

const NavigationRight = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
`;

const NavButton = styled(Button)`
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
  }
`;

const LogoutButton = styled(NavButton)`
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
  
  &:hover {
    background: rgba(239, 68, 68, 0.2);
    border-color: rgba(239, 68, 68, 0.4);
  }
`;

const StatusIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  padding: ${({ theme }) => theme.spacing[2]} ${({ theme }) => theme.spacing[3]};
  background: rgba(255, 255, 255, 0.1);
  border-radius: ${({ theme }) => theme.borderRadius.md};
  backdrop-filter: blur(10px);
`;

const StatusDot = styled.div<{ $status: 'online' | 'offline' }>`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: ${({ $status }) => $status === 'online' ? '#10b981' : '#ef4444'};
  animation: ${({ $status }) => $status === 'online' ? 'pulse 2s infinite' : 'none'};
  
  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }
`;

const WelcomeText = styled.div`
  color: rgba(255, 255, 255, 0.9);
  font-size: ${({ theme }) => theme.typography.fontSizes.sm};
`;

export const AdminNavigation: React.FC<AdminNavigationProps> = ({
  onPasswordManager,
  onBackupManager,
  onLogout
}) => {
  const currentTime = new Date().toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  });

  return (
    <NavigationContainer>
      <NavigationContent>
        <NavigationLeft>
          <Logo>
            <Typography variant="h4" weight="bold" style={{ color: 'white' }}>
              🎓 管理后台
            </Typography>
          </Logo>
          
          <NavigationMenu>
            <NavButton
              variant="outline"
              onClick={onPasswordManager}
              size="sm"
            >
              🔐 密码管理
            </NavButton>
            
            <NavButton
              variant="outline"
              onClick={onBackupManager}
              size="sm"
            >
              💾 备份管理
            </NavButton>
          </NavigationMenu>
        </NavigationLeft>

        <NavigationRight>
          <StatusIndicator>
            <StatusDot $status="online" />
            <WelcomeText>
              系统运行中 · {currentTime}
            </WelcomeText>
          </StatusIndicator>
          
          <LogoutButton
            variant="outline"
            onClick={onLogout}
            size="sm"
          >
            🚪 退出登录
          </LogoutButton>
        </NavigationRight>
      </NavigationContent>
    </NavigationContainer>
  );
};
