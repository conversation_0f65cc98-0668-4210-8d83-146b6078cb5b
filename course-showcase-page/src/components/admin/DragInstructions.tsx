import React from 'react';
import styled from 'styled-components';
import { Typography } from '../ui';

const InstructionsContainer = styled.div`
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #0ea5e9;
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  padding: ${({ theme }) => theme.spacing[6]};
  margin-bottom: ${({ theme }) => theme.spacing[6]};
`;

const InstructionsList = styled.ul`
  margin: ${({ theme }) => theme.spacing[4]} 0 0 0;
  padding-left: ${({ theme }) => theme.spacing[6]};
  
  li {
    margin-bottom: ${({ theme }) => theme.spacing[2]};
    font-family: ${({ theme }) => theme.typography.fonts.chinese};
    color: ${({ theme }) => theme.colors.gray[700]};
  }
`;

const IconText = styled.span`
  display: inline-flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  font-weight: ${({ theme }) => theme.typography.fontWeights.medium};
`;

export const DragInstructions: React.FC = () => {
  return (
    <InstructionsContainer>
      <Typography variant="h4" chinese weight="bold" color="#0ea5e9">
        <IconText>
          🎯 拖拽操作指南
        </IconText>
      </Typography>
      <InstructionsList>
        <li>
          <IconText>
            ⋮⋮ 拖拽模块标题左侧的图标来重新排序模块
          </IconText>
        </li>
        <li>
          <IconText>
            ⋮ 拖拽课程左侧的图标来重新排序课程
          </IconText>
        </li>
        <li>
          <IconText>
            💡 拖拽时会有视觉反馈，松开鼠标完成排序
          </IconText>
        </li>
        <li>
          <IconText>
            🔄 排序会自动保存到服务器
          </IconText>
        </li>
      </InstructionsList>
    </InstructionsContainer>
  );
};
