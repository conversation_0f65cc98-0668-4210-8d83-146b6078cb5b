import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { Typo<PERSON>, Button, Container } from '../ui';
import { ModuleEditor } from './ModuleEditor';
import { LessonEditor } from './LessonEditor';
import { LoginForm } from './LoginForm';
import { BackupManager } from './BackupManager';
import { BackupStatus } from './BackupStatus';
import { PasswordManager } from './PasswordManager';
import { AdminNavigation } from './AdminNavigation';
import type { CourseModule, Lesson } from '../../types';

interface CourseManagementProps {
  className?: string;
}

const ManagementContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: ${({ theme }) => theme.spacing[8]} 0;
`;

const Header = styled.div`
  background: ${({ theme }) => theme.colors.white};
  padding: ${({ theme }) => theme.spacing[6]};
  margin-bottom: ${({ theme }) => theme.spacing[8]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  box-shadow: ${({ theme }) => theme.shadows.base};
`;

const ModulesList = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[6]};
`;

const ModuleCard = styled.div`
  background: ${({ theme }) => theme.colors.white};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  padding: ${({ theme }) => theme.spacing[6]};
  box-shadow: ${({ theme }) => theme.shadows.base};
  border: 2px solid transparent;
  transition: all ${({ theme }) => theme.animations.duration.normal} ${({ theme }) => theme.animations.easing.easeOut};

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${({ theme }) => theme.shadows.lg};
  }
`;

const ModuleHeader = styled.div`
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing[4]};
  gap: ${({ theme }) => theme.spacing[4]};
`;

const ModuleInfo = styled.div`
  flex: 1;
`;

const ModuleActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const LessonsList = styled.div`
  margin-top: ${({ theme }) => theme.spacing[4]};
  padding-top: ${({ theme }) => theme.spacing[4]};
  border-top: 1px solid ${({ theme }) => theme.colors.gray[200]};
`;

const LessonItem = styled.div`
  display: flex;
  justify-content: between;
  align-items: center;
  padding: ${({ theme }) => theme.spacing[3]};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
  background: ${({ theme }) => theme.colors.gray[50]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  border: 1px solid transparent;
  
  &.dragging {
    border-color: ${({ theme }) => theme.colors.trustBlue};
    background: ${({ theme }) => theme.colors.trustBlue}10;
  }
`;

const LessonInfo = styled.div`
  flex: 1;
`;

const LessonActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const AddButton = styled(Button)`
  margin-top: ${({ theme }) => theme.spacing[4]};
`;

const BackupButton = styled(Button)`
  margin-left: ${({ theme }) => theme.spacing[4]};
`;

const DeleteButton = styled(Button)`
  color: #ef4444;
  border-color: #ef4444;

  &:hover {
    background-color: #ef4444;
    color: white;
  }
`;

const API_BASE = 'http://localhost:3001/api';

export const CourseManagement: React.FC<CourseManagementProps> = ({ className }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [modules, setModules] = useState<CourseModule[]>([]);
  const [loading, setLoading] = useState(false);
  const [editingModule, setEditingModule] = useState<CourseModule | null>(null);
  const [editingLesson, setEditingLesson] = useState<{ moduleId: string; lesson: Lesson | null }>({ moduleId: '', lesson: null });
  const [showBackupManager, setShowBackupManager] = useState(false);
  const [showPasswordManager, setShowPasswordManager] = useState(false);
  const [password, setPassword] = useState('');

  // 加载课程数据
  const loadCourseData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${API_BASE}/config/course-config`);
      const data = await response.json();
      setModules(data.modules || []);
    } catch (error) {
      console.error('加载课程数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isAuthenticated) {
      loadCourseData();
    }
  }, [isAuthenticated]);

  // 认证
  const handleLogin = (inputPassword: string) => {
    setPassword(inputPassword);
    setIsAuthenticated(true);
  };

  // 处理退出登录
  const handleLogout = () => {
    setIsAuthenticated(false);
    setPassword('');
    setModules([]);
    setShowBackupManager(false);
    setShowPasswordManager(false);
  };

  // 处理密码修改后
  const handlePasswordChanged = () => {
    // 密码修改后需要重新登录
    handleLogout();
  };

  // 添加模块
  const handleAddModule = () => {
    setEditingModule({
      id: '',
      title: '',
      description: '',
      color: 'growth',
      order: modules.length + 1,
      lessons: []
    } as CourseModule);
  };

  // 编辑模块
  const handleEditModule = (module: CourseModule) => {
    setEditingModule(module);
  };

  // 删除模块
  const handleDeleteModule = async (moduleId: string) => {
    if (!confirm('确定要删除这个模块吗？这将同时删除模块下的所有课程。')) {
      return;
    }

    try {
      const response = await fetch(`${API_BASE}/course/modules/${moduleId}?password=${password}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        await loadCourseData();
      } else {
        alert('删除失败');
      }
    } catch (error) {
      console.error('删除模块失败:', error);
      alert('删除失败');
    }
  };

  // 添加课程
  const handleAddLesson = (moduleId: string) => {
    setEditingLesson({
      moduleId,
      lesson: {
        id: '',
        title: '',
        description: '',
        duration: 0,
        audioUrl: '',
        order: 0,
        isCompleted: false
      } as Lesson
    });
  };

  // 编辑课程
  const handleEditLesson = (moduleId: string, lesson: Lesson) => {
    setEditingLesson({ moduleId, lesson });
  };

  // 删除课程
  const handleDeleteLesson = async (moduleId: string, lessonId: string) => {
    if (!confirm('确定要删除这个课程吗？')) {
      return;
    }

    try {
      const response = await fetch(`${API_BASE}/course/modules/${moduleId}/lessons/${lessonId}?password=${password}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        await loadCourseData();
      } else {
        alert('删除失败');
      }
    } catch (error) {
      console.error('删除课程失败:', error);
      alert('删除失败');
    }
  };

  if (!isAuthenticated) {
    return <LoginForm onLogin={handleLogin} />;
  }

  return (
    <ManagementContainer className={className}>
      <AdminNavigation
        onPasswordManager={() => setShowPasswordManager(true)}
        onBackupManager={() => setShowBackupManager(true)}
        onLogout={handleLogout}
      />

      <Container>
        <Header>
          <Typography variant="h1" chinese weight="bold" align="center">
            课程管理系统 📚
          </Typography>
          <Typography variant="body" chinese align="center" style={{ marginTop: '16px' }}>
            拖拽模块和课程来重新排序，点击编辑按钮来修改内容
          </Typography>
        </Header>

        <BackupStatus password={password} />

        {loading ? (
          <Typography variant="h3" chinese align="center">
            加载中...
          </Typography>
        ) : (
          <ModulesList>
            {modules.map((module) => (
              <ModuleCard key={module.id}>
                <ModuleHeader>
                  <ModuleInfo>
                    <Typography variant="h3" chinese weight="bold">
                      {module.title}
                    </Typography>
                    <Typography variant="body" chinese color="#666">
                      {module.description}
                    </Typography>
                  </ModuleInfo>
                  <ModuleActions>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditModule(module)}
                    >
                      编辑
                    </Button>
                    <DeleteButton
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteModule(module.id)}
                    >
                      删除
                    </DeleteButton>
                  </ModuleActions>
                </ModuleHeader>

                <LessonsList>
                  {module.lessons.map((lesson) => (
                    <LessonItem key={lesson.id}>
                      <LessonInfo>
                        <Typography variant="h6" chinese weight="medium">
                          {lesson.title}
                        </Typography>
                        <Typography variant="caption" chinese color="#666">
                          {Math.floor(lesson.duration / 60)}分钟 | {lesson.description}
                        </Typography>
                      </LessonInfo>
                      <LessonActions>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditLesson(module.id, lesson)}
                        >
                          编辑
                        </Button>
                        <DeleteButton
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteLesson(module.id, lesson.id)}
                        >
                          删除
                        </DeleteButton>
                      </LessonActions>
                    </LessonItem>
                  ))}
                  <AddButton
                    variant="outline"
                    onClick={() => handleAddLesson(module.id)}
                  >
                    + 添加课程
                  </AddButton>
                </LessonsList>
              </ModuleCard>
            ))}
          </ModulesList>
        )}

        <AddButton variant="primary" onClick={handleAddModule}>
          + 添加模块
        </AddButton>

        {/* 模块编辑器 */}
        {editingModule && (
          <ModuleEditor
            module={editingModule}
            password={password}
            onSave={() => {
              setEditingModule(null);
              loadCourseData();
            }}
            onCancel={() => setEditingModule(null)}
          />
        )}

        {/* 课程编辑器 */}
        {editingLesson.lesson && (
          <LessonEditor
            moduleId={editingLesson.moduleId}
            lesson={editingLesson.lesson}
            password={password}
            onSave={() => {
              setEditingLesson({ moduleId: '', lesson: null });
              loadCourseData();
            }}
            onCancel={() => setEditingLesson({ moduleId: '', lesson: null })}
          />
        )}

        {/* 备份管理器 */}
        {showBackupManager && (
          <BackupManager
            password={password}
            onClose={() => setShowBackupManager(false)}
          />
        )}

        {/* 密码管理器 */}
        {showPasswordManager && (
          <PasswordManager
            password={password}
            onClose={() => setShowPasswordManager(false)}
            onPasswordChanged={handlePasswordChanged}
          />
        )}
      </Container>
    </ManagementContainer>
  );
};
