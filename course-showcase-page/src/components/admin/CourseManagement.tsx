import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { Typography, Button, Container } from '../ui';
import { ModuleEditor } from './ModuleEditor';
import { LessonEditor } from './LessonEditor';
import { LoginForm } from './LoginForm';
import { DraggableModule } from './DraggableModule';
import { DragInstructions } from './DragInstructions';
import type { CourseModule, Lesson } from '../../types';

interface CourseManagementProps {
  className?: string;
}

const ManagementContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: ${({ theme }) => theme.spacing[8]} 0;
`;

const Header = styled.div`
  background: ${({ theme }) => theme.colors.white};
  padding: ${({ theme }) => theme.spacing[6]};
  margin-bottom: ${({ theme }) => theme.spacing[8]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  box-shadow: ${({ theme }) => theme.shadows.base};
`;

const ModulesList = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[6]};
`;

const AddButton = styled(Button)`
  margin-top: ${({ theme }) => theme.spacing[4]};
`;

const API_BASE = 'http://localhost:3001/api';

export const CourseManagement: React.FC<CourseManagementProps> = ({ className }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [modules, setModules] = useState<CourseModule[]>([]);
  const [loading, setLoading] = useState(false);
  const [editingModule, setEditingModule] = useState<CourseModule | null>(null);
  const [editingLesson, setEditingLesson] = useState<{ moduleId: string; lesson: Lesson | null }>({ moduleId: '', lesson: null });
  const [password, setPassword] = useState('');

  // 拖拽传感器配置
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // 加载课程数据
  const loadCourseData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${API_BASE}/config/course-config`);
      const data = await response.json();
      setModules(data.modules || []);
    } catch (error) {
      console.error('加载课程数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isAuthenticated) {
      loadCourseData();
    }
  }, [isAuthenticated]);

  // 认证
  const handleLogin = (inputPassword: string) => {
    setPassword(inputPassword);
    setIsAuthenticated(true);
  };

  // 模块拖拽处理
  const handleModuleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = modules.findIndex(module => module.id === active.id);
      const newIndex = modules.findIndex(module => module.id === over.id);

      const reorderedModules = arrayMove(modules, oldIndex, newIndex);
      setModules(reorderedModules);

      // 发送到服务器
      try {
        await fetch(`${API_BASE}/course/modules/reorder`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            password,
            moduleIds: reorderedModules.map(m => m.id)
          })
        });
      } catch (error) {
        console.error('更新模块排序失败:', error);
        // 回滚
        loadCourseData();
      }
    }
  };

  // 课程重新排序处理
  const handleReorderLessons = async (moduleId: string, lessonIds: string[]) => {
    try {
      await fetch(`${API_BASE}/course/modules/${moduleId}/lessons/reorder`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          password,
          lessonIds
        })
      });

      // 更新本地状态
      setModules(prevModules =>
        prevModules.map(module => {
          if (module.id === moduleId) {
            const reorderedLessons = lessonIds.map(id =>
              module.lessons.find(lesson => lesson.id === id)!
            );
            return { ...module, lessons: reorderedLessons };
          }
          return module;
        })
      );
    } catch (error) {
      console.error('更新课程排序失败:', error);
      // 回滚
      loadCourseData();
    }
  };

  // 添加模块
  const handleAddModule = () => {
    setEditingModule({
      id: '',
      title: '',
      description: '',
      color: 'growth',
      order: modules.length + 1,
      lessons: []
    } as CourseModule);
  };

  // 编辑模块
  const handleEditModule = (module: CourseModule) => {
    setEditingModule(module);
  };

  // 删除模块
  const handleDeleteModule = async (moduleId: string) => {
    if (!confirm('确定要删除这个模块吗？这将同时删除模块下的所有课程。')) {
      return;
    }

    try {
      const response = await fetch(`${API_BASE}/course/modules/${moduleId}?password=${password}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        await loadCourseData();
      } else {
        alert('删除失败');
      }
    } catch (error) {
      console.error('删除模块失败:', error);
      alert('删除失败');
    }
  };

  // 添加课程
  const handleAddLesson = (moduleId: string) => {
    setEditingLesson({
      moduleId,
      lesson: {
        id: '',
        title: '',
        description: '',
        duration: 0,
        audioUrl: '',
        order: 0,
        isCompleted: false
      } as Lesson
    });
  };

  // 编辑课程
  const handleEditLesson = (moduleId: string, lesson: Lesson) => {
    setEditingLesson({ moduleId, lesson });
  };

  // 删除课程
  const handleDeleteLesson = async (moduleId: string, lessonId: string) => {
    if (!confirm('确定要删除这个课程吗？')) {
      return;
    }

    try {
      const response = await fetch(`${API_BASE}/course/modules/${moduleId}/lessons/${lessonId}?password=${password}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        await loadCourseData();
      } else {
        alert('删除失败');
      }
    } catch (error) {
      console.error('删除课程失败:', error);
      alert('删除失败');
    }
  };

  if (!isAuthenticated) {
    return <LoginForm onLogin={handleLogin} />;
  }

  return (
    <ManagementContainer className={className}>
      <Container>
        <Header>
          <Typography variant="h1" chinese weight="bold" align="center">
            课程管理系统 📚
          </Typography>
          <Typography variant="body" chinese align="center" style={{ marginTop: '16px' }}>
            拖拽模块和课程来重新排序，点击编辑按钮来修改内容
          </Typography>
        </Header>

        <DragInstructions />

        {loading ? (
          <Typography variant="h3" chinese align="center">
            加载中...
          </Typography>
        ) : (
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleModuleDragEnd}
          >
            <SortableContext
              items={modules.map(module => module.id)}
              strategy={verticalListSortingStrategy}
            >
              <ModulesList>
                {modules.map((module) => (
                  <DraggableModule
                    key={module.id}
                    module={module}
                    onEditModule={handleEditModule}
                    onDeleteModule={handleDeleteModule}
                    onAddLesson={handleAddLesson}
                    onEditLesson={handleEditLesson}
                    onDeleteLesson={handleDeleteLesson}
                    onReorderLessons={handleReorderLessons}
                  />
                ))}
              </ModulesList>
            </SortableContext>
          </DndContext>
        )}

        <AddButton variant="primary" onClick={handleAddModule}>
          + 添加模块
        </AddButton>

        {/* 模块编辑器 */}
        {editingModule && (
          <ModuleEditor
            module={editingModule}
            password={password}
            onSave={() => {
              setEditingModule(null);
              loadCourseData();
            }}
            onCancel={() => setEditingModule(null)}
          />
        )}

        {/* 课程编辑器 */}
        {editingLesson.lesson && (
          <LessonEditor
            moduleId={editingLesson.moduleId}
            lesson={editingLesson.lesson}
            password={password}
            onSave={() => {
              setEditingLesson({ moduleId: '', lesson: null });
              loadCourseData();
            }}
            onCancel={() => setEditingLesson({ moduleId: '', lesson: null })}
          />
        )}
      </Container>
    </ManagementContainer>
  );
};
