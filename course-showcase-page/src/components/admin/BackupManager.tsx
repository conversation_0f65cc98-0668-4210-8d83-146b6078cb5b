import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { Typography, Button } from '../ui';

interface Backup {
  name: string;
  type: 'auto' | 'manual' | 'pre-change' | 'pre-restore';
  description: string;
  timestamp: string;
  files: Array<{
    original: string;
    backup: string;
    size: number;
  }>;
  isComplete: boolean;
}

interface BackupStats {
  total: number;
  auto: number;
  manual: number;
  preRestore: number;
  totalSize: number;
  oldestBackup: Backup | null;
  newestBackup: Backup | null;
  isAutoBackupEnabled: boolean;
  autoBackupInterval: number;
}

interface BackupManagerProps {
  password: string;
  onClose: () => void;
}

const BackupContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: ${({ theme }) => theme.spacing[4]};
`;

const BackupModal = styled.div`
  background: ${({ theme }) => theme.colors.white};
  border-radius: ${({ theme }) => theme.borderRadius.xl};
  padding: ${({ theme }) => theme.spacing[8]};
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
`;

const BackupHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing[6]};
`;

const BackupActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[4]};
  margin-bottom: ${({ theme }) => theme.spacing[6]};
  flex-wrap: wrap;
`;

const AutoBackupControls = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
  padding: ${({ theme }) => theme.spacing[4]};
  background: ${({ theme }) => theme.colors.gray[50]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  margin-bottom: ${({ theme }) => theme.spacing[4]};
`;

const ToggleSwitch = styled.label<{ $enabled: boolean }>`
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;

  input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  span {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: ${({ $enabled, theme }) =>
      $enabled ? theme.colors.growthGreen : theme.colors.gray[300]};
    transition: 0.3s;
    border-radius: 24px;

    &:before {
      position: absolute;
      content: "";
      height: 18px;
      width: 18px;
      left: ${({ $enabled }) => $enabled ? '29px' : '3px'};
      bottom: 3px;
      background-color: white;
      transition: 0.3s;
      border-radius: 50%;
    }
  }
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing[4]};
  margin-bottom: ${({ theme }) => theme.spacing[6]};
`;

const StatCard = styled.div`
  background: ${({ theme }) => theme.colors.gray[50]};
  padding: ${({ theme }) => theme.spacing[4]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  text-align: center;
`;

const BackupList = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[3]};
`;

const BackupItem = styled.div<{ $type: string }>`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${({ theme }) => theme.spacing[4]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  border: 1px solid ${({ theme }) => theme.colors.gray[200]};
  background: ${({ theme }) => theme.colors.white};
  
  ${({ $type, theme }) => {
    switch ($type) {
      case 'manual':
        return `border-left: 4px solid ${theme.colors.trustBlue};`;
      case 'auto':
        return `border-left: 4px solid ${theme.colors.growthGreen};`;
      case 'pre-change':
        return `border-left: 4px solid ${theme.colors.actionOrange};`;
      case 'pre-restore':
        return `border-left: 4px solid #8b5cf6;`;
      default:
        return '';
    }
  }}
`;

const BackupInfo = styled.div`
  flex: 1;
`;

const BackupMeta = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[4]};
  margin-top: ${({ theme }) => theme.spacing[2]};
`;

const BackupBadge = styled.span<{ $type: string }>`
  padding: 2px 8px;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-size: ${({ theme }) => theme.typography.fontSizes.xs};
  font-weight: ${({ theme }) => theme.typography.fontWeights.medium};
  
  ${({ $type, theme }) => {
    switch ($type) {
      case 'manual':
        return `background: ${theme.colors.trustBlue}20; color: ${theme.colors.trustBlue};`;
      case 'auto':
        return `background: ${theme.colors.growthGreen}20; color: ${theme.colors.growthGreen};`;
      case 'pre-change':
        return `background: ${theme.colors.actionOrange}20; color: ${theme.colors.actionOrange};`;
      case 'pre-restore':
        return `background: #8b5cf620; color: #8b5cf6;`;
      default:
        return `background: ${theme.colors.gray[200]}; color: ${theme.colors.gray[600]};`;
    }
  }}
`;

const BackupActions2 = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const API_BASE = 'http://localhost:3001/api';

export const BackupManager: React.FC<BackupManagerProps> = ({ password, onClose }) => {
  const [backups, setBackups] = useState<Backup[]>([]);
  const [stats, setStats] = useState<BackupStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [creating, setCreating] = useState(false);
  const [updatingSettings, setUpdatingSettings] = useState(false);

  // 加载备份列表
  const loadBackups = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${API_BASE}/backups?password=${password}`);
      const data = await response.json();
      
      if (data.success) {
        setBackups(data.backups);
      }
    } catch (error) {
      console.error('加载备份列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 加载统计信息
  const loadStats = async () => {
    try {
      const response = await fetch(`${API_BASE}/backups/stats?password=${password}`);
      const data = await response.json();
      
      if (data.success) {
        setStats(data.stats);
      }
    } catch (error) {
      console.error('加载备份统计失败:', error);
    }
  };

  useEffect(() => {
    loadBackups();
    loadStats();
  }, []);

  // 创建手动备份
  const createBackup = async () => {
    const description = prompt('请输入备份描述（可选）:') || '手动备份';
    
    try {
      setCreating(true);
      const response = await fetch(`${API_BASE}/backups`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password, description })
      });

      const data = await response.json();
      if (data.success) {
        await loadBackups();
        await loadStats();
        alert('备份创建成功！');
      } else {
        alert('备份创建失败: ' + data.error);
      }
    } catch (error) {
      console.error('创建备份失败:', error);
      alert('创建备份失败');
    } finally {
      setCreating(false);
    }
  };

  // 恢复备份
  const restoreBackup = async (backupName: string) => {
    if (!confirm(`确定要恢复备份 "${backupName}" 吗？这将覆盖当前的配置文件。`)) {
      return;
    }

    try {
      const response = await fetch(`${API_BASE}/backups/${backupName}/restore`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password })
      });

      const data = await response.json();
      if (data.success) {
        await loadBackups();
        await loadStats();
        alert('备份恢复成功！页面将刷新以加载新配置。');
        window.location.reload();
      } else {
        alert('备份恢复失败: ' + data.error);
      }
    } catch (error) {
      console.error('恢复备份失败:', error);
      alert('恢复备份失败');
    }
  };

  // 删除备份
  const deleteBackup = async (backupName: string) => {
    if (!confirm(`确定要删除备份 "${backupName}" 吗？此操作不可撤销。`)) {
      return;
    }

    try {
      const response = await fetch(`${API_BASE}/backups/${backupName}?password=${password}`, {
        method: 'DELETE'
      });

      const data = await response.json();
      if (data.success) {
        await loadBackups();
        await loadStats();
        alert('备份删除成功！');
      } else {
        alert('备份删除失败: ' + data.error);
      }
    } catch (error) {
      console.error('删除备份失败:', error);
      alert('删除备份失败');
    }
  };

  // 切换自动备份开关
  const toggleAutoBackup = async (enabled: boolean) => {
    try {
      setUpdatingSettings(true);
      const response = await fetch(`${API_BASE}/backups/settings`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          password,
          enableAutoBackup: enabled
        })
      });

      const data = await response.json();
      if (data.success) {
        await loadStats();
        alert(`自动备份已${enabled ? '启用' : '禁用'}！`);
      } else {
        alert('设置失败: ' + data.error);
      }
    } catch (error) {
      console.error('设置自动备份失败:', error);
      alert('设置失败');
    } finally {
      setUpdatingSettings(false);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('zh-CN');
  };

  const getTypeLabel = (type: string) => {
    const labels = {
      auto: '自动备份',
      manual: '手动备份',
      'pre-change': '修改前备份',
      'pre-restore': '恢复前备份'
    };
    return labels[type as keyof typeof labels] || type;
  };

  return (
    <BackupContainer onClick={(e) => e.target === e.currentTarget && onClose()}>
      <BackupModal>
        <BackupHeader>
          <Typography variant="h2" chinese weight="bold">
            备份管理 💾
          </Typography>
          <Button variant="outline" onClick={onClose}>
            关闭
          </Button>
        </BackupHeader>

        <AutoBackupControls>
          <Typography variant="h6" chinese weight="medium">
            ⚙️ 自动备份设置
          </Typography>
          <ToggleSwitch $enabled={stats?.isAutoBackupEnabled || false}>
            <input
              type="checkbox"
              checked={stats?.isAutoBackupEnabled || false}
              onChange={(e) => toggleAutoBackup(e.target.checked)}
              disabled={updatingSettings}
            />
            <span></span>
          </ToggleSwitch>
          <Typography variant="body" chinese color="#666">
            {stats?.isAutoBackupEnabled ? (
              `已启用 (每${stats.autoBackupInterval}分钟)`
            ) : (
              '已禁用'
            )}
          </Typography>
          {updatingSettings && (
            <Typography variant="caption" chinese color="#666">
              设置中...
            </Typography>
          )}
        </AutoBackupControls>

        <BackupActions>
          <Button
            variant="primary"
            onClick={createBackup}
            disabled={creating}
          >
            {creating ? '创建中...' : '创建备份'}
          </Button>
          <Button variant="outline" onClick={loadBackups}>
            刷新列表
          </Button>
        </BackupActions>

        {stats && (
          <StatsGrid>
            <StatCard>
              <Typography variant="h4" chinese weight="bold">
                {stats.total}
              </Typography>
              <Typography variant="caption" chinese color="#666">
                总备份数
              </Typography>
            </StatCard>
            <StatCard>
              <Typography variant="h4" chinese weight="bold">
                {stats.auto}
              </Typography>
              <Typography variant="caption" chinese color="#666">
                自动备份
              </Typography>
            </StatCard>
            <StatCard>
              <Typography variant="h4" chinese weight="bold">
                {stats.manual}
              </Typography>
              <Typography variant="caption" chinese color="#666">
                手动备份
              </Typography>
            </StatCard>
            <StatCard>
              <Typography variant="h4" chinese weight="bold">
                {formatFileSize(stats.totalSize)}
              </Typography>
              <Typography variant="caption" chinese color="#666">
                总大小
              </Typography>
            </StatCard>
          </StatsGrid>
        )}

        <Typography variant="h3" chinese weight="bold" style={{ marginBottom: '16px' }}>
          备份列表
        </Typography>

        {loading ? (
          <Typography variant="body" chinese align="center">
            加载中...
          </Typography>
        ) : (
          <BackupList>
            {backups.map((backup) => (
              <BackupItem key={backup.name} $type={backup.type}>
                <BackupInfo>
                  <Typography variant="h6" chinese weight="medium">
                    {backup.description || backup.name}
                  </Typography>
                  <BackupMeta>
                    <BackupBadge $type={backup.type}>
                      {getTypeLabel(backup.type)}
                    </BackupBadge>
                    <Typography variant="caption" chinese color="#666">
                      {formatDate(backup.timestamp)}
                    </Typography>
                    <Typography variant="caption" chinese color="#666">
                      {backup.files.length} 个文件
                    </Typography>
                    {!backup.isComplete && (
                      <Typography variant="caption" chinese color="#ef4444">
                        ⚠️ 不完整
                      </Typography>
                    )}
                  </BackupMeta>
                </BackupInfo>
                <BackupActions2>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => restoreBackup(backup.name)}
                    disabled={!backup.isComplete}
                  >
                    恢复
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => deleteBackup(backup.name)}
                    style={{ color: '#ef4444' }}
                  >
                    删除
                  </Button>
                </BackupActions2>
              </BackupItem>
            ))}
            {backups.length === 0 && (
              <Typography variant="body" chinese align="center" color="#666">
                暂无备份
              </Typography>
            )}
          </BackupList>
        )}
      </BackupModal>
    </BackupContainer>
  );
};
