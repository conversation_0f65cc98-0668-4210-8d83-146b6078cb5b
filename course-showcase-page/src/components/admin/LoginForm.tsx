import React, { useState } from 'react';
import styled from 'styled-components';
import { Typo<PERSON>, But<PERSON>, Container } from '../ui';
import { useAuth } from '../../hooks/useAuth';

interface LoginFormProps {
  onLogin: (token: string) => void;
}

const LoginContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
`;

const LoginCard = styled.div`
  background: ${({ theme }) => theme.colors.white};
  padding: ${({ theme }) => theme.spacing[12]};
  border-radius: ${({ theme }) => theme.borderRadius.xl};
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  width: 100%;
  max-width: 400px;
`;

const LoginHeader = styled.div`
  text-align: center;
  margin-bottom: ${({ theme }) => theme.spacing[8]};
`;

const LoginFormContainer = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[6]};
`;

const InputGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const Label = styled.label`
  font-family: ${({ theme }) => theme.typography.fonts.chinese};
  font-weight: ${({ theme }) => theme.typography.fontWeights.medium};
  color: ${({ theme }) => theme.colors.gray[700]};
`;

const Input = styled.input`
  padding: ${({ theme }) => theme.spacing[4]};
  border: 2px solid ${({ theme }) => theme.colors.gray[200]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  font-size: ${({ theme }) => theme.typography.fontSizes.lg};
  font-family: ${({ theme }) => theme.typography.fonts.chinese};
  transition: all ${({ theme }) => theme.animations.duration.fast} ${({ theme }) => theme.animations.easing.easeOut};
  
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.actionOrange};
    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors.actionOrange}20;
  }
  
  &::placeholder {
    color: ${({ theme }) => theme.colors.gray[400]};
  }
`;

const ErrorMessage = styled.div`
  color: #ef4444;
  font-size: ${({ theme }) => theme.typography.fontSizes.sm};
  font-family: ${({ theme }) => theme.typography.fonts.chinese};
  text-align: center;
  margin-top: ${({ theme }) => theme.spacing[2]};
`;

const LoginIcon = styled.div`
  font-size: 48px;
  margin-bottom: ${({ theme }) => theme.spacing[4]};
`;

export const LoginForm: React.FC<LoginFormProps> = ({ onLogin }) => {
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const { login } = useAuth();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!password.trim()) {
      setError('请输入密码');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const result = await login(password);

      if (result.success && result.token) {
        onLogin(result.token);
      } else {
        setError(result.error || '密码错误，请重试');
      }
    } catch (error) {
      console.error('登录失败:', error);
      setError('网络错误，请重试');
    }

    setLoading(false);
  };

  return (
    <LoginContainer>
      <Container>
        <LoginCard>
          <LoginHeader>
            <LoginIcon>🔐</LoginIcon>
            <Typography variant="h2" chinese weight="bold" align="center">
              课程管理系统
            </Typography>
            <Typography variant="body" chinese align="center" color="#666" style={{ marginTop: '8px' }}>
              请输入管理密码以继续
            </Typography>
          </LoginHeader>

          <LoginFormContainer onSubmit={handleSubmit}>
            <InputGroup>
              <Label htmlFor="password">管理密码</Label>
              <Input
                id="password"
                type="password"
                placeholder="请输入密码"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                disabled={loading}
              />
            </InputGroup>

            {error && <ErrorMessage>{error}</ErrorMessage>}

            <Button
              type="submit"
              variant="primary"
              size="lg"
              disabled={loading}
              style={{ width: '100%' }}
            >
              {loading ? '验证中...' : '登录'}
            </Button>
          </LoginFormContainer>

          <div style={{ marginTop: '24px', textAlign: 'center' }}>
            <Typography variant="caption" chinese color="#666">
              提示：默认密码为 "dabai"
            </Typography>
          </div>
        </LoginCard>
      </Container>
    </LoginContainer>
  );
};
