import React, { useState } from 'react';
import styled from 'styled-components';
import { Typography, Button } from '../ui';
import { useAuth } from '../../hooks/useAuth';
import type { Lesson } from '../../types';

interface LessonEditorProps {
  moduleId: string;
  lesson: Lesson;
  token: string | null;
  onSave: () => void;
  onCancel: () => void;
}

const EditorOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: ${({ theme }) => theme.spacing[4]};
`;

const EditorCard = styled.div`
  background: ${({ theme }) => theme.colors.white};
  border-radius: ${({ theme }) => theme.borderRadius.xl};
  padding: ${({ theme }) => theme.spacing[8]};
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
`;

const EditorHeader = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing[6]};
  text-align: center;
`;

const EditorForm = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[6]};
`;

const InputGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const Label = styled.label`
  font-family: ${({ theme }) => theme.typography.fonts.chinese};
  font-weight: ${({ theme }) => theme.typography.fontWeights.medium};
  color: ${({ theme }) => theme.colors.gray[700]};
`;

const Input = styled.input`
  padding: ${({ theme }) => theme.spacing[4]};
  border: 2px solid ${({ theme }) => theme.colors.gray[200]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  font-size: ${({ theme }) => theme.typography.fontSizes.lg};
  font-family: ${({ theme }) => theme.typography.fonts.chinese};
  transition: all ${({ theme }) => theme.animations.duration.fast} ${({ theme }) => theme.animations.easing.easeOut};
  
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.actionOrange};
    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors.actionOrange}20;
  }
`;

const Textarea = styled.textarea`
  padding: ${({ theme }) => theme.spacing[4]};
  border: 2px solid ${({ theme }) => theme.colors.gray[200]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  font-size: ${({ theme }) => theme.typography.fontSizes.lg};
  font-family: ${({ theme }) => theme.typography.fonts.chinese};
  min-height: 100px;
  resize: vertical;
  transition: all ${({ theme }) => theme.animations.duration.fast} ${({ theme }) => theme.animations.easing.easeOut};
  
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.actionOrange};
    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors.actionOrange}20;
  }
`;

const FileUploadGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[3]};
`;

const FileInput = styled.input`
  padding: ${({ theme }) => theme.spacing[3]};
  border: 2px dashed ${({ theme }) => theme.colors.gray[300]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  background: ${({ theme }) => theme.colors.gray[50]};
  font-family: ${({ theme }) => theme.typography.fonts.chinese};
  
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.actionOrange};
  }
`;

const DurationInputGroup = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${({ theme }) => theme.spacing[4]};
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[4]};
  justify-content: flex-end;
  margin-top: ${({ theme }) => theme.spacing[6]};
`;

const UploadProgress = styled.div`
  background: ${({ theme }) => theme.colors.gray[100]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing[2]};
  font-size: ${({ theme }) => theme.typography.fontSizes.sm};
  color: ${({ theme }) => theme.colors.gray[600]};
`;

const API_BASE = 'http://localhost:3001/api';

export const LessonEditor: React.FC<LessonEditorProps> = ({
  moduleId,
  lesson,
  token,
  onSave,
  onCancel
}) => {
  const { authenticatedFetch, getAuthHeaders } = useAuth();
  const [formData, setFormData] = useState({
    title: lesson.title,
    description: lesson.description || '',
    duration: lesson.duration,
    audioUrl: lesson.audioUrl
  });
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.trim()) {
      setError('请输入课程标题');
      return;
    }

    if (formData.duration <= 0) {
      setError('请输入有效的课程时长');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const isNew = !lesson.id;
      const url = isNew 
        ? `${API_BASE}/course/modules/${moduleId}/lessons`
        : `${API_BASE}/course/modules/${moduleId}/lessons/${lesson.id}`;
      
      const method = isNew ? 'POST' : 'PUT';
      
      const response = await authenticatedFetch(url, {
        method,
        body: JSON.stringify({
          lesson: formData
        })
      });

      if (response.ok) {
        onSave();
      } else {
        const errorData = await response.json();
        setError(errorData.error || '保存失败');
      }
    } catch (error) {
      console.error('保存课程失败:', error);
      setError('保存失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (field: string, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    setError('');
  };

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setUploading(true);
    setError('');

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('folder', 'audio');

      const response = await fetch(`${API_BASE}/upload`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: formData
      });

      if (response.ok) {
        const result = await response.json();
        handleChange('audioUrl', result.url);
      } else {
        const errorData = await response.json();
        setError(errorData.error || '文件上传失败');
      }
    } catch (error) {
      console.error('文件上传失败:', error);
      setError('文件上传失败，请重试');
    } finally {
      setUploading(false);
    }
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}分${remainingSeconds}秒`;
  };

  return (
    <EditorOverlay onClick={(e) => e.target === e.currentTarget && onCancel()}>
      <EditorCard>
        <EditorHeader>
          <Typography variant="h2" chinese weight="bold">
            {lesson.id ? '编辑课程' : '添加课程'} 🎓
          </Typography>
        </EditorHeader>

        <EditorForm onSubmit={handleSubmit}>
          <InputGroup>
            <Label htmlFor="title">课程标题 *</Label>
            <Input
              id="title"
              type="text"
              placeholder="例如：心理学的起源与发展"
              value={formData.title}
              onChange={(e) => handleChange('title', e.target.value)}
              disabled={loading}
            />
          </InputGroup>

          <InputGroup>
            <Label htmlFor="description">课程描述</Label>
            <Textarea
              id="description"
              placeholder="例如：了解心理学的历史发展脉络"
              value={formData.description}
              onChange={(e) => handleChange('description', e.target.value)}
              disabled={loading}
            />
          </InputGroup>

          <DurationInputGroup>
            <InputGroup>
              <Label htmlFor="duration">课程时长 (秒) *</Label>
              <Input
                id="duration"
                type="number"
                placeholder="1800"
                min="0"
                value={formData.duration}
                onChange={(e) => handleChange('duration', parseInt(e.target.value) || 0)}
                disabled={loading}
              />
            </InputGroup>
            <InputGroup>
              <Label>时长预览</Label>
              <Input
                type="text"
                value={formatDuration(formData.duration)}
                disabled
                style={{ background: '#f9fafb' }}
              />
            </InputGroup>
          </DurationInputGroup>

          <FileUploadGroup>
            <Label htmlFor="audioFile">音频文件</Label>
            <FileInput
              id="audioFile"
              type="file"
              accept="audio/*"
              onChange={handleFileUpload}
              disabled={loading || uploading}
            />
            {uploading && (
              <UploadProgress>
                正在上传音频文件...
              </UploadProgress>
            )}
            {formData.audioUrl && (
              <Typography variant="caption" chinese color="#059669">
                ✓ 音频文件：{formData.audioUrl}
              </Typography>
            )}
          </FileUploadGroup>

          <InputGroup>
            <Label htmlFor="audioUrl">音频URL (可选)</Label>
            <Input
              id="audioUrl"
              type="text"
              placeholder="/audio/01.mp3"
              value={formData.audioUrl}
              onChange={(e) => handleChange('audioUrl', e.target.value)}
              disabled={loading}
            />
            <Typography variant="caption" chinese color="#666">
              可以直接输入音频文件路径，或通过上面的文件上传功能
            </Typography>
          </InputGroup>

          {error && (
            <Typography variant="caption" chinese color="#ef4444" align="center">
              {error}
            </Typography>
          )}

          <ButtonGroup>
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={loading || uploading}
            >
              取消
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={loading || uploading}
            >
              {loading ? '保存中...' : '保存'}
            </Button>
          </ButtonGroup>
        </EditorForm>
      </EditorCard>
    </EditorOverlay>
  );
};
