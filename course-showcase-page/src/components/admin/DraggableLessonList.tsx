import React from 'react';
import styled from 'styled-components';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { DraggableLesson } from './DraggableLesson';
import type { Lesson } from '../../types';

interface DraggableLessonListProps {
  moduleId: string;
  lessons: Lesson[];
  onEditLesson: (moduleId: string, lesson: Lesson) => void;
  onDeleteLesson: (moduleId: string, lessonId: string) => void;
  onReorderLessons: (moduleId: string, lessonIds: string[]) => void;
}

const LessonsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[3]};
`;

export const DraggableLessonList: React.FC<DraggableLessonListProps> = ({
  moduleId,
  lessons,
  onEditLesson,
  onDeleteLesson,
  onReorderLessons
}) => {
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = lessons.findIndex(lesson => lesson.id === active.id);
      const newIndex = lessons.findIndex(lesson => lesson.id === over.id);
      
      const reorderedLessons = arrayMove(lessons, oldIndex, newIndex);
      const lessonIds = reorderedLessons.map(lesson => lesson.id);
      
      onReorderLessons(moduleId, lessonIds);
    }
  };

  if (lessons.length === 0) {
    return (
      <LessonsContainer>
        <div style={{ 
          padding: '16px', 
          textAlign: 'center', 
          color: '#666',
          fontStyle: 'italic',
          border: '2px dashed #e5e7eb',
          borderRadius: '8px'
        }}>
          暂无课程，点击下方按钮添加课程
        </div>
      </LessonsContainer>
    );
  }

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragEnd={handleDragEnd}
    >
      <SortableContext
        items={lessons.map(lesson => lesson.id)}
        strategy={verticalListSortingStrategy}
      >
        <LessonsContainer>
          {lessons.map((lesson) => (
            <DraggableLesson
              key={lesson.id}
              lesson={lesson}
              onEdit={() => onEditLesson(moduleId, lesson)}
              onDelete={() => onDeleteLesson(moduleId, lesson.id)}
            />
          ))}
        </LessonsContainer>
      </SortableContext>
    </DndContext>
  );
};
