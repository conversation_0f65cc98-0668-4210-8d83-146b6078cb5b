import React, { useState } from 'react';
import styled from 'styled-components';
import { Typography, Button } from '../ui';
import { useAuth } from '../../hooks/useAuth';
import type { CourseModule } from '../../types';

interface ModuleEditorProps {
  module: CourseModule;
  token: string | null;
  onSave: () => void;
  onCancel: () => void;
}

const EditorOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: ${({ theme }) => theme.spacing[4]};
`;

const EditorCard = styled.div`
  background: ${({ theme }) => theme.colors.white};
  border-radius: ${({ theme }) => theme.borderRadius.xl};
  padding: ${({ theme }) => theme.spacing[8]};
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
`;

const EditorHeader = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing[6]};
  text-align: center;
`;

const EditorForm = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[6]};
`;

const InputGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const Label = styled.label`
  font-family: ${({ theme }) => theme.typography.fonts.chinese};
  font-weight: ${({ theme }) => theme.typography.fontWeights.medium};
  color: ${({ theme }) => theme.colors.gray[700]};
`;

const Input = styled.input`
  padding: ${({ theme }) => theme.spacing[4]};
  border: 2px solid ${({ theme }) => theme.colors.gray[200]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  font-size: ${({ theme }) => theme.typography.fontSizes.lg};
  font-family: ${({ theme }) => theme.typography.fonts.chinese};
  transition: all ${({ theme }) => theme.animations.duration.fast} ${({ theme }) => theme.animations.easing.easeOut};
  
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.actionOrange};
    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors.actionOrange}20;
  }
`;

const Textarea = styled.textarea`
  padding: ${({ theme }) => theme.spacing[4]};
  border: 2px solid ${({ theme }) => theme.colors.gray[200]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  font-size: ${({ theme }) => theme.typography.fontSizes.lg};
  font-family: ${({ theme }) => theme.typography.fonts.chinese};
  min-height: 120px;
  resize: vertical;
  transition: all ${({ theme }) => theme.animations.duration.fast} ${({ theme }) => theme.animations.easing.easeOut};
  
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.actionOrange};
    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors.actionOrange}20;
  }
`;

const Select = styled.select`
  padding: ${({ theme }) => theme.spacing[4]};
  border: 2px solid ${({ theme }) => theme.colors.gray[200]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  font-size: ${({ theme }) => theme.typography.fontSizes.lg};
  font-family: ${({ theme }) => theme.typography.fonts.chinese};
  background: ${({ theme }) => theme.colors.white};
  transition: all ${({ theme }) => theme.animations.duration.fast} ${({ theme }) => theme.animations.easing.easeOut};
  
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.actionOrange};
    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors.actionOrange}20;
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[4]};
  justify-content: flex-end;
  margin-top: ${({ theme }) => theme.spacing[6]};
`;

const API_BASE = 'http://localhost:3001/api';

export const ModuleEditor: React.FC<ModuleEditorProps> = ({
  module,
  token,
  onSave,
  onCancel
}) => {
  const { authenticatedFetch } = useAuth();
  const [formData, setFormData] = useState({
    title: module.title,
    description: module.description,
    color: module.color
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.trim()) {
      setError('请输入模块标题');
      return;
    }

    if (!formData.description.trim()) {
      setError('请输入模块描述');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const isNew = !module.id;
      const url = isNew 
        ? `${API_BASE}/course/modules`
        : `${API_BASE}/course/modules/${module.id}`;
      
      const method = isNew ? 'POST' : 'PUT';
      
      const response = await authenticatedFetch(url, {
        method,
        body: JSON.stringify({
          module: formData
        })
      });

      if (response.ok) {
        onSave();
      } else {
        const errorData = await response.json();
        setError(errorData.error || '保存失败');
      }
    } catch (error) {
      console.error('保存模块失败:', error);
      setError('保存失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    setError('');
  };

  return (
    <EditorOverlay onClick={(e) => e.target === e.currentTarget && onCancel()}>
      <EditorCard>
        <EditorHeader>
          <Typography variant="h2" chinese weight="bold">
            {module.id ? '编辑模块' : '添加模块'} 📝
          </Typography>
        </EditorHeader>

        <EditorForm onSubmit={handleSubmit}>
          <InputGroup>
            <Label htmlFor="title">模块标题 *</Label>
            <Input
              id="title"
              type="text"
              placeholder="例如：理论篇"
              value={formData.title}
              onChange={(e) => handleChange('title', e.target.value)}
              disabled={loading}
            />
          </InputGroup>

          <InputGroup>
            <Label htmlFor="description">模块描述 *</Label>
            <Textarea
              id="description"
              placeholder="例如：快速构建你的咨询知识地图"
              value={formData.description}
              onChange={(e) => handleChange('description', e.target.value)}
              disabled={loading}
            />
          </InputGroup>

          <InputGroup>
            <Label htmlFor="color">模块颜色主题</Label>
            <Select
              id="color"
              value={formData.color}
              onChange={(e) => handleChange('color', e.target.value)}
              disabled={loading}
            >
              <option value="growth">成长绿 (Growth)</option>
              <option value="trust">信任蓝 (Trust)</option>
              <option value="action">行动橙 (Action)</option>
            </Select>
          </InputGroup>

          {error && (
            <Typography variant="caption" chinese color="#ef4444" align="center">
              {error}
            </Typography>
          )}

          <ButtonGroup>
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={loading}
            >
              取消
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={loading}
            >
              {loading ? '保存中...' : '保存'}
            </Button>
          </ButtonGroup>
        </EditorForm>
      </EditorCard>
    </EditorOverlay>
  );
};
