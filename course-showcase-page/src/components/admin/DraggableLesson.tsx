import React from 'react';
import styled from 'styled-components';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Typography, Button } from '../ui';
import type { Lesson } from '../../types';

interface DraggableLessonProps {
  lesson: Lesson;
  onEdit: () => void;
  onDelete: () => void;
}

const LessonItem = styled.div<{ $isDragging: boolean }>`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${({ theme }) => theme.spacing[4]};
  background: ${({ theme }) => theme.colors.gray[50]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  border: 1px solid transparent;
  transition: all ${({ theme }) => theme.animations.duration.fast} ${({ theme }) => theme.animations.easing.easeOut};
  
  &:hover {
    background: ${({ theme }) => theme.colors.gray[100]};
    transform: translateY(-1px);
    box-shadow: ${({ theme }) => theme.shadows.sm};
  }
  
  ${({ $isDragging }) => $isDragging && `
    border-color: #3b82f6;
    background: #dbeafe;
    transform: rotate(1deg);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    z-index: 1000;
  `}
`;

const LessonContent = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
  flex: 1;
`;

const DragHandle = styled.div<{ $isDragging: boolean }>`
  cursor: grab;
  padding: ${({ theme }) => theme.spacing[1]};
  color: ${({ theme }) => theme.colors.gray[400]};
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  transition: all ${({ theme }) => theme.animations.duration.fast} ${({ theme }) => theme.animations.easing.easeOut};
  
  &:hover {
    color: ${({ theme }) => theme.colors.gray[600]};
    background: ${({ theme }) => theme.colors.gray[200]};
  }
  
  &:active {
    cursor: grabbing;
  }
  
  ${({ $isDragging }) => $isDragging && `
    cursor: grabbing;
    color: #3b82f6;
  `}
`;

const LessonInfo = styled.div`
  flex: 1;
`;

const LessonActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const DeleteButton = styled(Button)`
  color: #ef4444;
  border-color: #ef4444;
  
  &:hover {
    background-color: #ef4444;
    color: white;
  }
`;

const LessonMeta = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  margin-top: ${({ theme }) => theme.spacing[1]};
`;

const DurationBadge = styled.span`
  background: ${({ theme }) => theme.colors.trustBlue}20;
  color: ${({ theme }) => theme.colors.trustBlue};
  padding: 2px 8px;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-size: ${({ theme }) => theme.typography.fontSizes.xs};
  font-weight: ${({ theme }) => theme.typography.fontWeights.medium};
`;

const AudioIndicator = styled.span`
  background: ${({ theme }) => theme.colors.growthGreen}20;
  color: ${({ theme }) => theme.colors.growthGreen};
  padding: 2px 8px;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-size: ${({ theme }) => theme.typography.fontSizes.xs};
  font-weight: ${({ theme }) => theme.typography.fontWeights.medium};
`;

export const DraggableLesson: React.FC<DraggableLessonProps> = ({
  lesson,
  onEdit,
  onDelete
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: lesson.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    if (minutes > 0) {
      return `${minutes}分${remainingSeconds > 0 ? remainingSeconds + '秒' : ''}`;
    }
    return `${remainingSeconds}秒`;
  };

  return (
    <LessonItem
      ref={setNodeRef}
      style={style}
      $isDragging={isDragging}
    >
      <LessonContent>
        <DragHandle
          {...attributes}
          {...listeners}
          $isDragging={isDragging}
          title="拖拽来重新排序课程"
        >
          ⋮
        </DragHandle>
        <LessonInfo>
          <Typography variant="h6" chinese weight="medium">
            {lesson.title}
          </Typography>
          <LessonMeta>
            <DurationBadge>
              {formatDuration(lesson.duration)}
            </DurationBadge>
            {lesson.audioUrl && (
              <AudioIndicator>
                🎵 有音频
              </AudioIndicator>
            )}
          </LessonMeta>
          {lesson.description && (
            <Typography variant="caption" chinese color="#666" style={{ marginTop: '4px' }}>
              {lesson.description}
            </Typography>
          )}
        </LessonInfo>
      </LessonContent>
      <LessonActions>
        <Button
          variant="outline"
          size="sm"
          onClick={onEdit}
        >
          编辑
        </Button>
        <DeleteButton
          variant="outline"
          size="sm"
          onClick={onDelete}
        >
          删除
        </DeleteButton>
      </LessonActions>
    </LessonItem>
  );
};
