import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { Typography, Button } from '../ui';

interface PasswordInfo {
  isDefault: boolean;
  lastModified: string | null;
  version: string;
  passwordLength: number;
}

interface PasswordManagerProps {
  password: string;
  onClose: () => void;
  onPasswordChanged: () => void;
}

const PasswordContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: ${({ theme }) => theme.spacing[4]};
`;

const PasswordModal = styled.div`
  background: ${({ theme }) => theme.colors.white};
  border-radius: ${({ theme }) => theme.borderRadius.xl};
  padding: ${({ theme }) => theme.spacing[8]};
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
`;

const PasswordHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing[6]};
`;

const FormGroup = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing[4]};
`;

const Label = styled.label`
  display: block;
  margin-bottom: ${({ theme }) => theme.spacing[2]};
  font-weight: ${({ theme }) => theme.typography.fontWeights.medium};
  color: ${({ theme }) => theme.colors.gray[700]};
`;

const Input = styled.input`
  width: 100%;
  padding: ${({ theme }) => theme.spacing[3]};
  border: 1px solid ${({ theme }) => theme.colors.gray[300]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-size: ${({ theme }) => theme.typography.fontSizes.base};
  
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.trustBlue};
    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors.trustBlue}20;
  }
  
  &:invalid {
    border-color: ${({ theme }) => theme.colors.red[500]};
  }
`;

const InfoCard = styled.div`
  background: ${({ theme }) => theme.colors.gray[50]};
  padding: ${({ theme }) => theme.spacing[4]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  margin-bottom: ${({ theme }) => theme.spacing[6]};
`;

const InfoRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing[2]};
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const StatusBadge = styled.span<{ $isDefault: boolean }>`
  padding: 2px 8px;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-size: ${({ theme }) => theme.typography.fontSizes.xs};
  font-weight: ${({ theme }) => theme.typography.fontWeights.medium};
  background: ${({ $isDefault, theme }) => 
    $isDefault ? theme.colors.actionOrange + '20' : theme.colors.growthGreen + '20'};
  color: ${({ $isDefault, theme }) => 
    $isDefault ? theme.colors.actionOrange : theme.colors.growthGreen};
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[3]};
  margin-top: ${({ theme }) => theme.spacing[6]};
`;

const HelpText = styled.div`
  font-size: ${({ theme }) => theme.typography.fontSizes.sm};
  color: ${({ theme }) => theme.colors.gray[600]};
  margin-top: ${({ theme }) => theme.spacing[2]};
`;

const API_BASE = 'http://localhost:3001/api';

export const PasswordManager: React.FC<PasswordManagerProps> = ({ 
  password, 
  onClose, 
  onPasswordChanged 
}) => {
  const [passwordInfo, setPasswordInfo] = useState<PasswordInfo | null>(null);
  const [oldPassword, setOldPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [changing, setChanging] = useState(false);

  // 加载密码信息
  const loadPasswordInfo = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${API_BASE}/password/info?password=${password}`);
      const data = await response.json();
      
      if (data.success) {
        setPasswordInfo(data.info);
      }
    } catch (error) {
      console.error('加载密码信息失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadPasswordInfo();
  }, []);

  // 修改密码
  const changePassword = async () => {
    if (newPassword !== confirmPassword) {
      alert('新密码和确认密码不一致');
      return;
    }

    if (newPassword.length < 4 || newPassword.length > 20) {
      alert('密码长度必须在4-20位之间');
      return;
    }

    try {
      setChanging(true);
      const response = await fetch(`${API_BASE}/password/change`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          password,
          oldPassword,
          newPassword
        })
      });

      const data = await response.json();
      if (data.success) {
        alert('密码修改成功！请使用新密码重新登录。');
        onPasswordChanged();
        onClose();
      } else {
        alert('密码修改失败: ' + data.error);
      }
    } catch (error) {
      console.error('修改密码失败:', error);
      alert('修改密码失败');
    } finally {
      setChanging(false);
    }
  };

  // 重置密码
  const resetPassword = async () => {
    if (!confirm('确定要重置密码为默认值吗？')) {
      return;
    }

    try {
      const response = await fetch(`${API_BASE}/password/reset`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password })
      });

      const data = await response.json();
      if (data.success) {
        alert(`密码已重置为默认值: ${data.result.defaultPassword}`);
        await loadPasswordInfo();
      } else {
        alert('重置密码失败: ' + data.error);
      }
    } catch (error) {
      console.error('重置密码失败:', error);
      alert('重置密码失败');
    }
  };

  // 生成随机密码
  const generatePassword = async () => {
    try {
      const response = await fetch(`${API_BASE}/password/generate`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password, length: 8 })
      });

      const data = await response.json();
      if (data.success) {
        setNewPassword(data.password);
        setConfirmPassword(data.password);
      } else {
        alert('生成密码失败: ' + data.error);
      }
    } catch (error) {
      console.error('生成密码失败:', error);
      alert('生成密码失败');
    }
  };

  const formatDate = (timestamp: string | null) => {
    if (!timestamp) return '未知';
    return new Date(timestamp).toLocaleString('zh-CN');
  };

  return (
    <PasswordContainer onClick={(e) => e.target === e.currentTarget && onClose()}>
      <PasswordModal>
        <PasswordHeader>
          <Typography variant="h2" chinese weight="bold">
            密码管理 🔐
          </Typography>
          <Button variant="outline" onClick={onClose}>
            关闭
          </Button>
        </PasswordHeader>

        {loading ? (
          <Typography variant="body" chinese align="center">
            加载中...
          </Typography>
        ) : passwordInfo ? (
          <>
            <InfoCard>
              <Typography variant="h6" chinese weight="medium" style={{ marginBottom: '12px' }}>
                当前密码状态
              </Typography>
              <InfoRow>
                <Typography variant="body" chinese>状态</Typography>
                <StatusBadge $isDefault={passwordInfo.isDefault}>
                  {passwordInfo.isDefault ? '默认密码' : '自定义密码'}
                </StatusBadge>
              </InfoRow>
              <InfoRow>
                <Typography variant="body" chinese>密码长度</Typography>
                <Typography variant="body" chinese>{passwordInfo.passwordLength} 位</Typography>
              </InfoRow>
              <InfoRow>
                <Typography variant="body" chinese>最后修改</Typography>
                <Typography variant="body" chinese>{formatDate(passwordInfo.lastModified)}</Typography>
              </InfoRow>
            </InfoCard>

            <Typography variant="h6" chinese weight="medium" style={{ marginBottom: '16px' }}>
              修改密码
            </Typography>

            <FormGroup>
              <Label>当前密码</Label>
              <Input
                type="password"
                value={oldPassword}
                onChange={(e) => setOldPassword(e.target.value)}
                placeholder="请输入当前密码"
              />
            </FormGroup>

            <FormGroup>
              <Label>新密码</Label>
              <Input
                type="password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                placeholder="请输入新密码（4-20位）"
                minLength={4}
                maxLength={20}
              />
              <HelpText>
                密码长度4-20位，支持字母、数字和中文
              </HelpText>
            </FormGroup>

            <FormGroup>
              <Label>确认新密码</Label>
              <Input
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                placeholder="请再次输入新密码"
              />
            </FormGroup>

            <ButtonGroup>
              <Button
                variant="primary"
                onClick={changePassword}
                disabled={changing || !oldPassword || !newPassword || !confirmPassword}
              >
                {changing ? '修改中...' : '修改密码'}
              </Button>
              <Button
                variant="outline"
                onClick={generatePassword}
              >
                生成随机密码
              </Button>
              <Button
                variant="outline"
                onClick={resetPassword}
                style={{ color: '#ef4444' }}
              >
                重置为默认
              </Button>
            </ButtonGroup>
          </>
        ) : (
          <Typography variant="body" chinese align="center" color="#ef4444">
            无法加载密码信息
          </Typography>
        )}
      </PasswordModal>
    </PasswordContainer>
  );
};
