import React from 'react';
import styled from 'styled-components';
import { useAudio } from '../hooks';

const PlayerContainer = styled.div`
  position: fixed;
  bottom: 20px;
  left: 20px;
  background: white;
  border: 2px solid #28a745;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  z-index: 9998;
  max-width: 400px;
  font-family: ${({ theme }) => theme.typography.fonts.chinese};
`;

const PlayerTitle = styled.h4`
  margin: 0 0 10px 0;
  color: #28a745;
  font-size: 14px;
`;

const StatusText = styled.div<{ $type: 'info' | 'success' | 'error' }>`
  padding: 8px;
  margin: 8px 0;
  border-radius: 4px;
  font-size: 12px;
  
  ${({ $type }) => {
    switch ($type) {
      case 'success':
        return 'background: #d4edda; color: #155724;';
      case 'error':
        return 'background: #f8d7da; color: #721c24;';
      default:
        return 'background: #d1ecf1; color: #0c5460;';
    }
  }}
`;

const ControlButton = styled.button`
  background: #28a745;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  margin: 2px;
  font-size: 12px;
  
  &:hover {
    background: #218838;
  }
  
  &:disabled {
    background: #ccc;
    cursor: not-allowed;
  }
`;

const AudioInfo = styled.div`
  font-size: 11px;
  color: #666;
  margin: 5px 0;
`;

export const SimpleAudioPlayer: React.FC = () => {
  const { state, pauseAudio, resumeAudio } = useAudio();

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getStatusType = (): 'info' | 'success' | 'error' => {
    if (state.error) return 'error';
    if (state.isPlaying) return 'success';
    return 'info';
  };

  const getStatusMessage = (): string => {
    if (state.error) return `错误: ${state.error}`;
    if (state.isLoading) return '加载中...';
    if (!state.currentLesson) return '未选择课程';
    if (state.isPlaying) return `正在播放: ${state.currentLesson.title}`;
    return `已暂停: ${state.currentLesson.title}`;
  };

  if (!state.currentLesson) {
    return (
      <PlayerContainer>
        <PlayerTitle>🎵 音频播放器状态</PlayerTitle>
        <StatusText $type="info">
          请点击课程大纲中的任意课程开始播放
        </StatusText>
      </PlayerContainer>
    );
  }

  return (
    <PlayerContainer>
      <PlayerTitle>🎵 音频播放器状态</PlayerTitle>
      
      <StatusText $type={getStatusType()}>
        {getStatusMessage()}
      </StatusText>
      
      <AudioInfo>
        <div><strong>课程:</strong> {state.currentLesson.title}</div>
        <div><strong>音频:</strong> {state.currentLesson.audioUrl}</div>
        <div><strong>时长:</strong> {state.duration > 0 ? formatTime(state.duration) : '未知'}</div>
        <div><strong>进度:</strong> {formatTime(state.currentTime)} / {formatTime(state.duration)}</div>
      </AudioInfo>
      
      <div>
        {state.isPlaying ? (
          <ControlButton onClick={pauseAudio}>
            暂停
          </ControlButton>
        ) : (
          <ControlButton onClick={resumeAudio} disabled={state.isLoading}>
            播放
          </ControlButton>
        )}
        
        <ControlButton 
          onClick={() => window.location.reload()} 
          style={{ background: '#dc3545' }}
        >
          重置
        </ControlButton>
      </div>
      
      <AudioInfo>
        <div><strong>调试信息:</strong></div>
        <div>isPlaying: {state.isPlaying ? '是' : '否'}</div>
        <div>isLoading: {state.isLoading ? '是' : '否'}</div>
        <div>currentIndex: {state.currentIndex}</div>
        <div>playlist: {state.playlist.length} 个课程</div>
      </AudioInfo>
    </PlayerContainer>
  );
};

export default SimpleAudioPlayer;