import React, { useState } from 'react';
import styled, { css } from 'styled-components';
import { Typography, Container } from './ui';
import { courseData } from '../data';

interface HeaderProps {
  className?: string;
}

// Header container with background and positioning
const HeaderContainer = styled.header`
  position: relative;
  background: linear-gradient(135deg, ${({ theme }) => theme.colors.warmSand} 0%, #f5f0e8 100%);
  overflow: hidden;
  display: flex;
  flex-direction: column;
`;

// Navigation bar
const NavBar = styled.nav`
  position: relative;
  z-index: 10;
  padding: ${({ theme }) => theme.spacing[4]} 0;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(106, 141, 175, 0.1);
`;

const NavContainer = styled(Container)`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

// Brand logo/title
const Brand = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const BrandText = styled(Typography)`
  color: ${({ theme }) => theme.colors.trustBlue};
  font-weight: ${({ theme }) => theme.typography.fontWeights.bold};
  text-decoration: none;
  
  &:hover {
    color: ${({ theme }) => theme.colors.actionOrange};
    transition: color ${({ theme }) => theme.animations.duration.normal} ${({ theme }) => theme.animations.easing.easeOut};
  }
`;

// Desktop navigation menu
const NavMenu = styled.ul`
  display: none;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: ${({ theme }) => theme.spacing[6]};
  
  ${({ theme }) => theme.mediaQueries.tablet} {
    display: flex;
  }
`;

const NavItem = styled.li`
  margin: 0;
`;

const NavLink = styled.a`
  color: ${({ theme }) => theme.colors.trustBlue};
  text-decoration: none;
  font-family: ${({ theme }) => theme.typography.fonts.chinese};
  font-weight: ${({ theme }) => theme.typography.fontWeights.medium};
  padding: ${({ theme }) => theme.spacing[2]} ${({ theme }) => theme.spacing[3]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  transition: all ${({ theme }) => theme.animations.duration.normal} ${({ theme }) => theme.animations.easing.easeOut};
  
  &:hover {
    background-color: rgba(106, 141, 175, 0.1);
    color: ${({ theme }) => theme.colors.actionOrange};
  }
  
  &:focus {
    outline: 2px solid ${({ theme }) => theme.colors.trustBlue};
    outline-offset: 2px;
  }
`;

// Mobile menu button
const MobileMenuButton = styled.button`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: ${({ theme }) => theme.touchTargets.minimum};
  height: ${({ theme }) => theme.touchTargets.minimum};
  background: none;
  border: none;
  cursor: pointer;
  padding: ${({ theme }) => theme.spacing[2]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  transition: background-color ${({ theme }) => theme.animations.duration.normal};
  
  &:hover {
    background-color: rgba(106, 141, 175, 0.1);
  }
  
  &:focus {
    outline: 2px solid ${({ theme }) => theme.colors.trustBlue};
    outline-offset: 2px;
  }
  
  ${({ theme }) => theme.mediaQueries.tablet} {
    display: none;
  }
`;

const HamburgerLine = styled.span.withConfig({
  shouldForwardProp: (prop) => !['isOpen', 'index'].includes(prop)
})<{ isOpen: boolean; index: number }>`
  display: block;
  width: 20px;
  height: 2px;
  background-color: ${({ theme }) => theme.colors.trustBlue};
  margin: 2px 0;
  transition: all ${({ theme }) => theme.animations.duration.normal} ${({ theme }) => theme.animations.easing.easeInOut};
  transform-origin: center;
  
  ${({ isOpen, index }) => isOpen && index === 0 && css`
    transform: rotate(45deg) translate(5px, 5px);
  `}
  
  ${({ isOpen, index }) => isOpen && index === 1 && css`
    opacity: 0;
  `}
  
  ${({ isOpen, index }) => isOpen && index === 2 && css`
    transform: rotate(-45deg) translate(7px, -6px);
  `}
`;

// Mobile menu overlay
const MobileMenu = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== 'isOpen'
})<{ isOpen: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(240, 235, 227, 0.98);
  backdrop-filter: blur(10px);
  z-index: 20;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[6]};
  transform: ${({ isOpen }) => isOpen ? 'translateX(0)' : 'translateX(100%)'};
  transition: transform ${({ theme }) => theme.animations.duration.normal} ${({ theme }) => theme.animations.easing.easeInOut};
  
  ${({ theme }) => theme.mediaQueries.tablet} {
    display: none;
  }
`;

const MobileNavLink = styled.a`
  color: ${({ theme }) => theme.colors.trustBlue};
  text-decoration: none;
  font-family: ${({ theme }) => theme.typography.fonts.chinese};
  font-size: ${({ theme }) => theme.typography.fontSizes.xl};
  font-weight: ${({ theme }) => theme.typography.fontWeights.medium};
  padding: ${({ theme }) => theme.spacing[4]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  transition: all ${({ theme }) => theme.animations.duration.normal} ${({ theme }) => theme.animations.easing.easeOut};
  min-width: ${({ theme }) => theme.touchTargets.minimum};
  min-height: ${({ theme }) => theme.touchTargets.minimum};
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    background-color: rgba(106, 141, 175, 0.1);
    color: ${({ theme }) => theme.colors.actionOrange};
  }
  
  &:focus {
    outline: 2px solid ${({ theme }) => theme.colors.trustBlue};
    outline-offset: 2px;
  }
`;

// Hero section
const HeroSection = styled.section`
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: ${({ theme }) => theme.spacing[8]} 0 ${({ theme }) => theme.spacing[12]} 0;
  text-align: center;
  min-height: 400px;
  
  ${({ theme }) => theme.mediaQueries.tablet} {
    padding: ${({ theme }) => theme.spacing[12]} 0 ${({ theme }) => theme.spacing[16]} 0;
    min-height: 500px;
  }
  
  ${({ theme }) => theme.mediaQueries.desktop} {
    padding: ${({ theme }) => theme.spacing[16]} 0 ${({ theme }) => theme.spacing[20]} 0;
    min-height: 600px;
  }
`;

const HeroContent = styled.div`
  position: relative;
  z-index: 5;
  max-width: 800px;
`;

const CourseTitle = styled(Typography)`
  margin-bottom: ${({ theme }) => theme.spacing[4]};
  color: ${({ theme }) => theme.colors.trustBlue};
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  ${({ theme }) => theme.mediaQueries.mobile} {
    margin-bottom: ${({ theme }) => theme.spacing[3]};
  }
`;

const CourseSubtitle = styled(Typography)`
  color: ${({ theme }) => theme.colors.gray[600]};
  max-width: 600px;
  margin: 0 auto;
  line-height: ${({ theme }) => theme.typography.lineHeights.relaxed};
  
  /* 增大桌面端字号 */
  ${({ theme }) => theme.mediaQueries.desktop} {
    font-size: ${({ theme }) => theme.typography.fontSizes.xl};
  }
  
  /* 增大平板端字号 */
  ${({ theme }) => theme.mediaQueries.tablet} {
    font-size: ${({ theme }) => theme.typography.fontSizes.lg};
  }
  
  /* 增大移动端字号 */
  ${({ theme }) => theme.mediaQueries.mobile} {
    max-width: 90%;
    font-size: ${({ theme }) => theme.typography.fontSizes.base};
  }
`;

// Organic background shapes
const BackgroundShape = styled.div<{ variant: 'circle' | 'blob1' | 'blob2'; position: string }>`
  position: absolute;
  border-radius: 50%;
  opacity: 0.6;
  z-index: 1;
  
  ${({ variant, theme }) => variant === 'circle' && css`
    width: 200px;
    height: 200px;
    background: linear-gradient(135deg, ${theme.colors.growthGreen}, ${theme.colors.actionOrange});
  `}
  
  ${({ variant, theme }) => variant === 'blob1' && css`
    width: 300px;
    height: 250px;
    background: linear-gradient(45deg, ${theme.colors.trustBlue}, ${theme.colors.growthGreen});
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    animation: float 6s ease-in-out infinite;
  `}
  
  ${({ variant, theme }) => variant === 'blob2' && css`
    width: 180px;
    height: 220px;
    background: linear-gradient(-45deg, ${theme.colors.actionOrange}, ${theme.colors.warmSand});
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    animation: float 8s ease-in-out infinite reverse;
  `}
  
  ${({ position }) => position};
  
  @keyframes float {
    0%, 100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-20px) rotate(5deg);
    }
  }
  
  ${({ theme }) => theme.mediaQueries.mobile} {
    opacity: 0.2;
    
    ${({ variant }) => variant === 'circle' && css`
      width: 80px;
      height: 80px;
    `}
    
    ${({ variant }) => variant === 'blob1' && css`
      width: 120px;
      height: 100px;
    `}
    
    ${({ variant }) => variant === 'blob2' && css`
      width: 60px;
      height: 80px;
    `}
  }
  
  ${({ theme }) => theme.mediaQueries.tablet} {
    opacity: 0.4;
    
    ${({ variant }) => variant === 'circle' && css`
      width: 150px;
      height: 150px;
    `}
    
    ${({ variant }) => variant === 'blob1' && css`
      width: 220px;
      height: 180px;
    `}
    
    ${({ variant }) => variant === 'blob2' && css`
      width: 120px;
      height: 150px;
    `}
  }
`;

export const Header: React.FC<HeaderProps> = ({ className }) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  // Handle keyboard navigation for mobile menu
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Escape') {
      closeMobileMenu();
    }
  };

  return (
    <HeaderContainer className={className} onKeyDown={handleKeyDown}>
      {/* Navigation Bar */}
      <NavBar role="navigation" aria-label="主导航">
        <NavContainer>
          <Brand>
            <BrandText variant="h4" chinese>
              遇见之屿
            </BrandText>
          </Brand>
          
          {/* Desktop Navigation */}
          <NavMenu role="menubar">
            <NavItem role="none">
              <NavLink href="#teacher" role="menuitem">
                老师介绍
              </NavLink>
            </NavItem>
            <NavItem role="none">
              <NavLink href="#positioning" role="menuitem">
                课程定位
              </NavLink>
            </NavItem>
            <NavItem role="none">
              <NavLink href="#outline" role="menuitem">
                课程大纲
              </NavLink>
            </NavItem>
            <NavItem role="none">
              <NavLink href="#enrollment" role="menuitem">
                联系我们
              </NavLink>
            </NavItem>
          </NavMenu>
          
          {/* Mobile Menu Button */}
          <MobileMenuButton
            onClick={toggleMobileMenu}
            aria-label={isMobileMenuOpen ? '关闭菜单' : '打开菜单'}
            aria-expanded={isMobileMenuOpen}
            aria-controls="mobile-menu"
          >
            <HamburgerLine isOpen={isMobileMenuOpen} index={0} />
            <HamburgerLine isOpen={isMobileMenuOpen} index={1} />
            <HamburgerLine isOpen={isMobileMenuOpen} index={2} />
          </MobileMenuButton>
        </NavContainer>
      </NavBar>

      {/* Mobile Menu Overlay */}
      <MobileMenu 
        id="mobile-menu"
        isOpen={isMobileMenuOpen}
        role="dialog"
        aria-modal="true"
        aria-label="移动端导航菜单"
      >
        <MobileNavLink href="#teacher" onClick={closeMobileMenu}>
          老师介绍
        </MobileNavLink>
        <MobileNavLink href="#positioning" onClick={closeMobileMenu}>
          课程定位
        </MobileNavLink>
        <MobileNavLink href="#outline" onClick={closeMobileMenu}>
          课程大纲
        </MobileNavLink>
        <MobileNavLink href="#enrollment" onClick={closeMobileMenu}>
          联系我们
        </MobileNavLink>
      </MobileMenu>

      {/* Hero Section */}
      <HeroSection>
        <Container>
          <HeroContent>
            <CourseTitle variant="h1" chinese>
              {courseData.title}
            </CourseTitle>
            <CourseSubtitle variant="h6" chinese>
              {courseData.subtitle}
            </CourseSubtitle>
          </HeroContent>
        </Container>
        
        {/* Organic Background Shapes */}
        <BackgroundShape 
          variant="circle" 
          position="top: 15%; left: 8%;"
        />
        <BackgroundShape 
          variant="blob1" 
          position="top: 45%; right: 10%;"
        />
        <BackgroundShape 
          variant="blob2" 
          position="bottom: 20%; left: 15%;"
        />
      </HeroSection>
    </HeaderContainer>
  );
};

export default Header;