import React from 'react';
import styled, { keyframes } from 'styled-components';
import { Typography } from './ui';

// 加载动画
const spin = keyframes`
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
`;

const pulse = keyframes`
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
`;

const shimmer = keyframes`
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
`;

// 基础加载容器
const LoadingContainer = styled.div<{ $minHeight?: string }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing[8]};
  min-height: ${({ $minHeight }) => $minHeight || '200px'};
  text-align: center;
`;

// 旋转加载器
const Spinner = styled.div<{ $size?: 'sm' | 'md' | 'lg' }>`
  width: ${({ $size }) => {
    switch ($size) {
      case 'sm': return '20px';
      case 'lg': return '48px';
      default: return '32px';
    }
  }};
  height: ${({ $size }) => {
    switch ($size) {
      case 'sm': return '20px';
      case 'lg': return '48px';
      default: return '32px';
    }
  }};
  border: 3px solid ${({ theme }) => theme.colors.gray[200]};
  border-top: 3px solid ${({ theme }) => theme.colors.trustBlue};
  border-radius: 50%;
  animation: ${spin} 1s linear infinite;
  margin-bottom: ${({ theme }) => theme.spacing[3]};
`;

// 脉冲加载器
const PulseLoader = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[1]};
  margin-bottom: ${({ theme }) => theme.spacing[3]};
`;

const PulseDot = styled.div<{ $delay: number }>`
  width: 12px;
  height: 12px;
  background: ${({ theme }) => theme.colors.trustBlue};
  border-radius: 50%;
  animation: ${pulse} 1.4s ease-in-out infinite;
  animation-delay: ${({ $delay }) => $delay}s;
`;

// 骨架屏加载器
const SkeletonContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[3]};
  width: 100%;
`;

const SkeletonLine = styled.div<{ $width?: string; $height?: string }>`
  height: ${({ $height }) => $height || '16px'};
  width: ${({ $width }) => $width || '100%'};
  background: linear-gradient(
    90deg,
    ${({ theme }) => theme.colors.gray[200]} 0%,
    ${({ theme }) => theme.colors.gray[100]} 50%,
    ${({ theme }) => theme.colors.gray[200]} 100%
  );
  background-size: 200px 100%;
  animation: ${shimmer} 1.5s ease-in-out infinite;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
`;

const SkeletonCircle = styled.div<{ $size?: string }>`
  width: ${({ $size }) => $size || '48px'};
  height: ${({ $size }) => $size || '48px'};
  background: linear-gradient(
    90deg,
    ${({ theme }) => theme.colors.gray[200]} 0%,
    ${({ theme }) => theme.colors.gray[100]} 50%,
    ${({ theme }) => theme.colors.gray[200]} 100%
  );
  background-size: 200px 100%;
  animation: ${shimmer} 1.5s ease-in-out infinite;
  border-radius: 50%;
`;

// 加载文本
const LoadingText = styled(Typography)`
  color: ${({ theme }) => theme.colors.gray[600]};
  margin-top: ${({ theme }) => theme.spacing[2]};
`;

// 进度条
const ProgressContainer = styled.div`
  width: 100%;
  max-width: 300px;
  margin: ${({ theme }) => theme.spacing[4]} 0;
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 8px;
  background: ${({ theme }) => theme.colors.gray[200]};
  border-radius: ${({ theme }) => theme.borderRadius.full};
  overflow: hidden;
`;

const ProgressFill = styled.div<{ $progress: number }>`
  height: 100%;
  width: ${({ $progress }) => $progress}%;
  background: ${({ theme }) => theme.colors.trustBlue};
  border-radius: ${({ theme }) => theme.borderRadius.full};
  transition: width 0.3s ease;
`;

const ProgressText = styled.div`
  text-align: center;
  margin-top: ${({ theme }) => theme.spacing[2]};
  font-size: ${({ theme }) => theme.typography.fontSizes.sm};
  color: ${({ theme }) => theme.colors.gray[600]};
`;

// 组件接口
interface SpinnerLoadingProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  minHeight?: string;
}

interface PulseLoadingProps {
  text?: string;
  minHeight?: string;
}

interface SkeletonLoadingProps {
  lines?: number;
  showAvatar?: boolean;
  avatarSize?: string;
}

interface ProgressLoadingProps {
  progress: number;
  text?: string;
  showPercentage?: boolean;
}

// 旋转加载组件
export const SpinnerLoading: React.FC<SpinnerLoadingProps> = ({
  size = 'md',
  text = '加载中...',
  minHeight
}) => (
  <LoadingContainer $minHeight={minHeight} role="status" aria-live="polite">
    <Spinner $size={size} />
    <LoadingText variant="body" chinese>
      {text}
    </LoadingText>
  </LoadingContainer>
);

// 脉冲加载组件
export const PulseLoading: React.FC<PulseLoadingProps> = ({
  text = '加载中...',
  minHeight
}) => (
  <LoadingContainer $minHeight={minHeight} role="status" aria-live="polite">
    <PulseLoader>
      <PulseDot $delay={0} />
      <PulseDot $delay={0.2} />
      <PulseDot $delay={0.4} />
    </PulseLoader>
    <LoadingText variant="body" chinese>
      {text}
    </LoadingText>
  </LoadingContainer>
);

// 骨架屏加载组件
export const SkeletonLoading: React.FC<SkeletonLoadingProps> = ({
  lines = 3,
  showAvatar = false,
  avatarSize = '48px'
}) => (
  <SkeletonContainer role="status" aria-label="内容加载中">
    {showAvatar && (
      <div style={{ display: 'flex', alignItems: 'center', gap: '16px', marginBottom: '16px' }}>
        <SkeletonCircle $size={avatarSize} />
        <div style={{ flex: 1 }}>
          <SkeletonLine $width="60%" $height="20px" />
          <div style={{ marginTop: '8px' }}>
            <SkeletonLine $width="40%" $height="16px" />
          </div>
        </div>
      </div>
    )}
    
    {Array.from({ length: lines }, (_, index) => (
      <SkeletonLine
        key={index}
        $width={index === lines - 1 ? '75%' : '100%'}
        $height="16px"
      />
    ))}
  </SkeletonContainer>
);

// 进度加载组件
export const ProgressLoading: React.FC<ProgressLoadingProps> = ({
  progress,
  text = '加载中...',
  showPercentage = true
}) => (
  <LoadingContainer role="status" aria-live="polite">
    <LoadingText variant="body" chinese>
      {text}
    </LoadingText>
    <ProgressContainer>
      <ProgressBar>
        <ProgressFill $progress={progress} />
      </ProgressBar>
      {showPercentage && (
        <ProgressText>
          {Math.round(progress)}%
        </ProgressText>
      )}
    </ProgressContainer>
  </LoadingContainer>
);

// 音频加载专用组件
export const AudioLoading: React.FC<{ lessonTitle?: string; progress?: number }> = ({
  lessonTitle,
  progress
}) => (
  <LoadingContainer $minHeight="120px" role="status" aria-live="polite">
    <div style={{ fontSize: '32px', marginBottom: '16px' }}>🎵</div>
    <LoadingText variant="body" chinese>
      {lessonTitle ? `正在加载: ${lessonTitle}` : '音频加载中...'}
    </LoadingText>
    {typeof progress === 'number' && (
      <ProgressContainer>
        <ProgressBar>
          <ProgressFill $progress={progress} />
        </ProgressBar>
        <ProgressText>
          {Math.round(progress)}%
        </ProgressText>
      </ProgressContainer>
    )}
  </LoadingContainer>
);

// 页面加载组件
export const PageLoading: React.FC<{ text?: string }> = ({
  text = '页面加载中...'
}) => (
  <LoadingContainer $minHeight="100vh" role="status" aria-live="polite">
    <Spinner $size="lg" />
    <LoadingText variant="h6" chinese>
      {text}
    </LoadingText>
  </LoadingContainer>
);

export default {
  SpinnerLoading,
  PulseLoading,
  SkeletonLoading,
  ProgressLoading,
  AudioLoading,
  PageLoading
};