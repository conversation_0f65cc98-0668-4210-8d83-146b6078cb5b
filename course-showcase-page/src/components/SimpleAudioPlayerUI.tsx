import React, { useState, useRef, useEffect } from 'react';
import styled, { keyframes } from 'styled-components';
import { useSimpleAudio } from '../hooks/useSimpleAudio';
import { getAllLessons } from '../data';

// SVG图标组件
const PlayIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
    <path d="M8 5v14l11-7z"/>
  </svg>
);

const PauseIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
    <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
  </svg>
);

// 上一节/下一节图标
const PreviousTrackIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
    <path d="M6 6h2v12H6zm3.5 6l8.5 6V6z"/>
  </svg>
);

const NextTrackIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
    <path d="M6 18l8.5-6L6 6v12zM16 6v12h2V6h-2z"/>
  </svg>
);

const VolumeHighIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
    <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
  </svg>
);

const VolumeMediumIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
    <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02z"/>
  </svg>
);

const VolumeMuteIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
    <path d="M7 9v6h4l5 5V4l-5 5H7z"/>
  </svg>
);

const ExpandIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
    <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"/>
  </svg>
);

const CollapseIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 8l-6 6 1.41 1.41L12 10.83l4.59 4.58L18 14z"/>
  </svg>
);

// 动画效果
const slideUp = keyframes`
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
`;

const slideDown = keyframes`
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(100%);
    opacity: 0;
  }
`;

const pulse = keyframes`
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
`;

const waveAnimation = keyframes`
  0%, 100% {
    height: 4px;
  }
  50% {
    height: 12px;
  }
`;

const PlayerContainer = styled.div<{ $isVisible: boolean; $isExpanded: boolean }>`
  position: fixed;
  /* 使用品牌色：暖沙色到白色的渐变，营造温暖感 */
  background: linear-gradient(135deg, 
    #F0EBE3 0%, 
    ${({ theme }) => theme.colors.white} 100%);
  backdrop-filter: blur(20px);
  /* 使用治愈绿作为边框色 */
  border-top: 1px solid #A3B899;
  /* 柔和阴影，符合设计规范 */
  box-shadow: 0 -8px 32px rgba(0, 0, 0, 0.08);
  padding: ${({ theme }) => theme.spacing[4]};
  z-index: 9999;
  display: ${({ $isVisible }) => ($isVisible ? 'block' : 'none')};
  animation: ${({ $isVisible }) => $isVisible ? slideUp : slideDown} 0.3s ease-out;
  transition: all 0.3s ease-out;
  
  /* 默认移动端样式 - 吸底显示 */
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  
  /* 支持安全区域 */
  padding-bottom: calc(${({ theme }) => theme.spacing[4]} + env(safe-area-inset-bottom, 0));
  
  /* 确保在所有移动设备上正确吸底 */
  @supports (-webkit-touch-callout: none) {
    /* iOS Safari */
    bottom: env(safe-area-inset-bottom, 0);
  }
  
  /* 小屏手机 */
  @media (max-width: 480px) {
    padding: ${({ theme }) => theme.spacing[3]};
    padding-bottom: calc(${({ theme }) => theme.spacing[3]} + env(safe-area-inset-bottom, 0));
  }
  
  /* 平板端 */
  ${({ theme }) => theme.mediaQueries.tablet} {
    padding: ${({ theme }) => theme.spacing[4]};
    padding-bottom: calc(${({ theme }) => theme.spacing[4]} + env(safe-area-inset-bottom, 0));
  }
  
  /* 桌面端 - 侧边悬浮 */
  ${({ theme }) => theme.mediaQueries.desktop} {
    top: 50%;
    right: ${({ theme }) => theme.spacing[6]};
    bottom: auto;
    left: auto;
    width: ${({ $isExpanded }) => $isExpanded ? '400px' : '320px'};
    transform: translateY(-50%);
    border-radius: ${({ theme }) => theme.borderRadius.xl};
    border: 1px solid ${({ theme }) => theme.colors.border};
    animation: none;
    padding-bottom: ${({ theme }) => theme.spacing[4]};
  }
`;

const PlayerHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${({ theme }) => theme.spacing[3]};
`;

const PlayerInfo = styled.div`
  flex: 1;
  min-width: 0;
  margin-right: ${({ theme }) => theme.spacing[3]};
`;

const LessonTitle = styled.div`
  font-family: ${({ theme }) => theme.typography.fonts.chinese};
  font-size: ${({ theme }) => theme.typography.fontSizes.base};
  font-weight: ${({ theme }) => theme.typography.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.gray[900]};
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: ${({ theme }) => theme.spacing[1]};
`;

const LessonSubtitle = styled.div`
  font-family: ${({ theme }) => theme.typography.fonts.chinese};
  font-size: ${({ theme }) => theme.typography.fontSizes.sm};
  color: ${({ theme }) => theme.colors.gray[600]};
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const ExpandButton = styled.button`
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  color: ${({ theme }) => theme.colors.gray[600]};
  cursor: pointer;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease-out;
  
  &:hover {
    background: ${({ theme }) => theme.colors.gray[100]};
    color: ${({ theme }) => theme.colors.gray[800]};
  }
  
  /* 在移动端也显示展开按钮 */
  ${({ theme }) => theme.mediaQueries.mobile} {
    display: flex;
    width: 28px;
    height: 28px;
  }
`;

const MainControls = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[4]};
  margin-bottom: ${({ theme }) => theme.spacing[3]};
`;

const PlayButton = styled.button<{ $isPlaying: boolean }>`
  width: 56px;
  height: 56px;
  border-radius: 50%;
  border: none;
  /* 使用品牌色：信赖蓝到暖阳橙的渐变 */
  background: linear-gradient(135deg, 
    #6A8DAF 0%, 
    #F7B787 100%);
  color: ${({ theme }) => theme.colors.white};
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  transition: all 0.3s ease-out;
  /* 柔和阴影，符合设计规范 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  animation: ${({ $isPlaying }) => $isPlaying ? pulse : 'none'} 2s infinite;
  
  &:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
    /* 悬停时轻微透明度变化 */
    opacity: 0.9;
  }
  
  &:focus {
    outline: 2px solid #6A8DAF;
    outline-offset: 2px;
  }
  
  &:active {
    transform: scale(0.95);
  }
`;

const SecondaryControls = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  flex: 1;
`;

const ControlButton = styled.button`
  width: 40px;
  height: 40px;
  border: none;
  /* 使用暖沙色背景 */
  background: #F0EBE3;
  /* 使用信赖蓝作为图标颜色 */
  color: #6A8DAF;
  cursor: pointer;
  /* 圆角设计，符合规范 */
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: all 0.3s ease-out;
  /* 柔和阴影 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  
  &:hover:not(:disabled) {
    /* 悬停时使用治愈绿背景 */
    background: #A3B899;
    color: ${({ theme }) => theme.colors.white};
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  }
  
  &:focus {
    outline: 2px solid #6A8DAF;
    outline-offset: 2px;
  }
  
  &:active:not(:disabled) {
    transform: translateY(0);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: #F0EBE3;
    color: #CCCCCC;
    transform: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }
`;

const ProgressSection = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing[3]};
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 6px;
  /* 使用暖沙色作为背景 */
  background: #F0EBE3;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  margin-bottom: ${({ theme }) => theme.spacing[2]};
  position: relative;
  /* 柔和阴影 */
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
  
  &:hover {
    height: 8px;
    /* 悬停时轻微高亮 */
    background: #EDE7DF;
  }
`;

const ProgressFill = styled.div<{ $progress: number }>`
  height: 100%;
  /* 使用品牌色渐变：信赖蓝到暖阳橙 */
  background: linear-gradient(90deg, 
    #6A8DAF 0%, 
    #F7B787 100%);
  width: ${({ $progress }) => $progress}%;
  transition: width 0.1s ease-out;
  position: relative;
  border-radius: inherit;
  
  &::after {
    content: '';
    position: absolute;
    right: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    background: ${({ theme }) => theme.colors.white};
    /* 使用暖阳橙作为边框 */
    border: 3px solid #F7B787;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    opacity: ${({ $progress }) => $progress > 0 ? 1 : 0};
    transition: opacity 0.2s ease-out;
  }
`;

const TimeInfo = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const TimeDisplay = styled.div`
  font-family: ${({ theme }) => theme.typography.fonts.english};
  font-size: ${({ theme }) => theme.typography.fontSizes.sm};
  font-weight: ${({ theme }) => theme.typography.fontWeights.medium};
  color: ${({ theme }) => theme.colors.gray[700]};
  white-space: nowrap;
`;

const WaveformContainer = styled.div<{ $isPlaying: boolean }>`
  display: flex;
  align-items: center;
  gap: 2px;
  margin: 0 ${({ theme }) => theme.spacing[3]};
`;

const WaveBar = styled.div<{ $delay: number; $isActive: boolean }>`
  width: 3px;
  height: 4px;
  background: ${({ $isActive }) => 
    $isActive ? '#A3B899' : '#F0EBE3'};
  border-radius: 2px;
  animation: ${({ $isActive }) => $isActive ? waveAnimation : 'none'} 
    1.5s ease-in-out infinite;
  animation-delay: ${({ $delay }) => $delay}s;
  transition: background-color 0.3s ease-out;
`;

const VolumeControl = styled.div<{ $isExpanded: boolean }>`
  display: ${({ $isExpanded }) => $isExpanded ? 'flex' : 'none'};
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
  margin-top: ${({ theme }) => theme.spacing[3]};
  padding-top: ${({ theme }) => theme.spacing[3]};
  /* 使用暖沙色作为分割线 */
  border-top: 1px solid #F0EBE3;
`;

const VolumeSlider = styled.input`
  flex: 1;
  height: 6px;
  /* 使用暖沙色背景 */
  background: #F0EBE3;
  border-radius: 8px;
  outline: none;
  -webkit-appearance: none;
  cursor: pointer;
  
  &::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    /* 使用治愈绿 */
    background: #A3B899;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.2s ease-out;
  }
  
  &::-webkit-slider-thumb:hover {
    background: #8FA085;
    transform: scale(1.1);
  }
  
  &::-moz-range-thumb {
    width: 18px;
    height: 18px;
    background: #A3B899;
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  }
  
  &::-moz-range-track {
    background: #F0EBE3;
    height: 6px;
    border-radius: 8px;
  }
`;

const SpeedControl = styled.select`
  padding: 8px 12px;
  border: 1px solid #A3B899;
  border-radius: 8px;
  background: ${({ theme }) => theme.colors.white};
  font-family: ${({ theme }) => theme.typography.fonts.english};
  font-size: ${({ theme }) => theme.typography.fontSizes.sm};
  /* 使用主文字色 */
  color: #333333;
  cursor: pointer;
  transition: all 0.2s ease-out;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  
  &:hover {
    border-color: #6A8DAF;
    background: #F0EBE3;
  }
  
  &:focus {
    outline: 2px solid #6A8DAF;
    outline-offset: 2px;
    border-color: #6A8DAF;
  }
`;

export const SimpleAudioPlayerUI: React.FC = () => {
  const { state, pauseAudio, resumeAudio, seekTo, playLesson, setVolume: setAudioVolume, setPlaybackRate: setAudioPlaybackRate, setOnAudioEnded } = useSimpleAudio();
  const [isExpanded, setIsExpanded] = useState(false);
  const [previousVolume, setPreviousVolume] = useState(0.8);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  
  // 使用hook中的音量和播放速度状态
  const volume = state.volume;
  const playbackRate = state.playbackRate;
  
  // 使用ref来存储最新的值，供回调函数使用
  const stateRef = useRef(state);
  const playLessonRef = useRef(playLesson);
  
  // 更新ref中的值
  useEffect(() => {
    stateRef.current = state;
    playLessonRef.current = playLesson;
  }, [state, playLesson]);
  
  // 获取所有课程
  const allLessons = getAllLessons();
  
  // 获取当前课程在列表中的索引
  const getCurrentLessonIndex = () => {
    if (!state.currentLesson) return -1;
    return allLessons.findIndex(lesson => lesson.id === state.currentLesson?.id);
  };
  
  // 检查是否有可用的音频文件
  const hasAudioFile = (lesson: any) => {
    return lesson && lesson.audioUrl && lesson.audioUrl.includes('.mp3');
  };

  // 获取音频元素引用 - 从useSimpleAudio hook中获取
  useEffect(() => {
    // 等待一下让音频元素创建完成
    const timer = setTimeout(() => {
      const audioElement = document.querySelector('audio');
      if (audioElement) {
        audioRef.current = audioElement;
        console.log('Audio element found and referenced');
      }
    }, 100);
    
    return () => clearTimeout(timer);
  }, [state.currentLesson]);

  // 设置自动播放下一节的回调 - 使用ref获取最新值
  useEffect(() => {
    const handleAutoPlayNext = () => {
      console.log('Auto-play: trying to play next lesson...');
      
      // 从ref中获取最新的状态和函数
      const currentState = stateRef.current;
      const currentPlayLesson = playLessonRef.current;
      
      const currentLessonId = currentState.currentLesson?.id;
      if (!currentLessonId) {
        console.log('Auto-play: No current lesson');
        return;
      }
      
      const currentIndex = allLessons.findIndex(lesson => lesson.id === currentLessonId);
      if (currentIndex === -1 || currentIndex >= allLessons.length - 1) {
        console.log('Auto-play: No more lessons to play');
        return;
      }
      
      // 从当前位置向后查找有音频文件的课程
      for (let i = currentIndex + 1; i < allLessons.length; i++) {
        const lesson = allLessons[i];
        if (lesson && lesson.audioUrl && lesson.audioUrl.includes('.mp3')) {
          console.log('Auto-play: Playing next lesson:', lesson.title);
          currentPlayLesson(lesson);
          return;
        }
      }
      console.log('Auto-play: No more lessons with audio files');
    };

    // 设置自动播放回调
    setOnAudioEnded(handleAutoPlayNext);

    // 清理函数：移除回调
    return () => {
      setOnAudioEnded(null);
    };
  }, []); // 空依赖数组，只在组件挂载时执行一次

  // 音量控制现在通过hook处理，不需要单独的useEffect

  // 播放速度控制现在通过hook处理，不需要单独的useEffect

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleProgressClick = (event: React.MouseEvent<HTMLDivElement>) => {
    const rect = event.currentTarget.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const percentage = clickX / rect.width;
    const newTime = percentage * state.duration;
    seekTo(newTime);
  };

  const handleVolumeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(event.target.value);
    console.log('Volume slider changed to:', newVolume);
    setAudioVolume(newVolume);
  };

  const handleSpeedChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const newRate = parseFloat(event.target.value);
    console.log('UI: Playback speed changed to:', newRate);
    setAudioPlaybackRate(newRate);
  };

  const toggleMute = () => {
    console.log('Toggle mute clicked, current volume:', volume);
    if (volume > 0) {
      setPreviousVolume(volume);
      console.log('Muting, saving previous volume:', volume);
      setAudioVolume(0);
    } else {
      const newVolume = previousVolume > 0 ? previousVolume : 0.8;
      console.log('Unmuting, restoring volume to:', newVolume);
      setAudioVolume(newVolume);
    }
  };

  // 播放上一节
  const playPreviousTrack = () => {
    const currentIndex = getCurrentLessonIndex();
    if (currentIndex <= 0) return; // 已经是第一节
    
    // 从当前位置向前查找有音频文件的课程
    for (let i = currentIndex - 1; i >= 0; i--) {
      const lesson = allLessons[i];
      if (hasAudioFile(lesson)) {
        playLesson(lesson);
        return;
      }
    }
  };

  // 播放下一节
  const playNextTrack = () => {
    const currentIndex = getCurrentLessonIndex();
    if (currentIndex === -1 || currentIndex >= allLessons.length - 1) return; // 已经是最后一节
    
    // 从当前位置向后查找有音频文件的课程
    for (let i = currentIndex + 1; i < allLessons.length; i++) {
      const lesson = allLessons[i];
      if (hasAudioFile(lesson)) {
        playLesson(lesson);
        return;
      }
    }
  };

  // 检查是否有上一节
  const hasPreviousTrack = () => {
    const currentIndex = getCurrentLessonIndex();
    if (currentIndex <= 0) return false;
    
    // 检查前面是否有可播放的音频
    for (let i = currentIndex - 1; i >= 0; i--) {
      if (hasAudioFile(allLessons[i])) {
        return true;
      }
    }
    return false;
  };

  // 检查是否有下一节
  const hasNextTrack = () => {
    const currentIndex = getCurrentLessonIndex();
    if (currentIndex === -1 || currentIndex >= allLessons.length - 1) return false;
    
    // 检查后面是否有可播放的音频
    for (let i = currentIndex + 1; i < allLessons.length; i++) {
      if (hasAudioFile(allLessons[i])) {
        return true;
      }
    }
    return false;
  };

  const progress = state.duration > 0 ? (state.currentTime / state.duration) * 100 : 0;
  const isVisible = state.currentLesson !== null;

  if (!isVisible) return null;

  return (
    <PlayerContainer 
      $isVisible={isVisible} 
      $isExpanded={isExpanded}
      role="region" 
      aria-label="音频播放器"
    >
      <PlayerHeader>
        <PlayerInfo>
          <LessonTitle title={state.currentLesson?.title}>
            {state.currentLesson?.title}
          </LessonTitle>
          <LessonSubtitle>
            课程音频 • {formatTime(state.duration)}
          </LessonSubtitle>
          {state.error && (
            <div style={{ 
              color: '#dc3545', 
              fontSize: '12px', 
              marginTop: '4px',
              padding: '4px 8px',
              background: '#ffeaea',
              borderRadius: '4px'
            }}>
              {state.error}
            </div>
          )}
        </PlayerInfo>
        
        <ExpandButton
          onClick={() => setIsExpanded(!isExpanded)}
          aria-label={isExpanded ? '收起播放器' : '展开播放器'}
        >
          {isExpanded ? <CollapseIcon /> : <ExpandIcon />}
        </ExpandButton>
      </PlayerHeader>

      <MainControls>
        <PlayButton
          $isPlaying={state.isPlaying}
          onClick={state.isPlaying ? pauseAudio : resumeAudio}
          aria-label={state.isPlaying ? '暂停播放' : '继续播放'}
        >
          {state.isPlaying ? <PauseIcon /> : <PlayIcon />}
        </PlayButton>

        <SecondaryControls>
          <ControlButton
            onClick={playPreviousTrack}
            disabled={!hasPreviousTrack()}
            aria-label="上一节"
            title="上一节"
          >
            <PreviousTrackIcon />
          </ControlButton>

          <WaveformContainer $isPlaying={state.isPlaying}>
            {[...Array(8)].map((_, i) => (
              <WaveBar
                key={i}
                $delay={i * 0.1}
                $isActive={state.isPlaying}
              />
            ))}
          </WaveformContainer>

          <ControlButton
            onClick={playNextTrack}
            disabled={!hasNextTrack()}
            aria-label="下一节"
            title="下一节"
          >
            <NextTrackIcon />
          </ControlButton>

          <ControlButton
            onClick={toggleMute}
            aria-label={volume > 0 ? '静音' : '取消静音'}
            title={volume > 0 ? '静音' : '取消静音'}
          >
            {volume > 0.5 ? <VolumeHighIcon /> : volume > 0 ? <VolumeMediumIcon /> : <VolumeMuteIcon />}
          </ControlButton>
        </SecondaryControls>
      </MainControls>

      <ProgressSection>
        <ProgressBar 
          onClick={handleProgressClick} 
          role="progressbar" 
          aria-label="播放进度"
          aria-valuenow={progress}
          aria-valuemin={0}
          aria-valuemax={100}
        >
          <ProgressFill $progress={progress} />
        </ProgressBar>
        
        <TimeInfo>
          <TimeDisplay>
            {formatTime(state.currentTime)}
          </TimeDisplay>
          <TimeDisplay>
            {formatTime(state.duration)}
          </TimeDisplay>
        </TimeInfo>
      </ProgressSection>

      <VolumeControl $isExpanded={isExpanded}>
        <ControlButton
          onClick={toggleMute}
          aria-label={volume > 0 ? '静音' : '取消静音'}
        >
          {volume > 0.5 ? <VolumeHighIcon /> : volume > 0 ? <VolumeMediumIcon /> : <VolumeMuteIcon />}
        </ControlButton>
        
        <VolumeSlider
          type="range"
          min="0"
          max="1"
          step="0.1"
          value={volume}
          onChange={handleVolumeChange}
          aria-label="音量控制"
        />
        
        <SpeedControl
          value={playbackRate}
          onChange={handleSpeedChange}
          aria-label="播放速度"
        >
          <option value="0.5">0.5x</option>
          <option value="0.75">0.75x</option>
          <option value="1">1x</option>
          <option value="1.25">1.25x</option>
          <option value="1.5">1.5x</option>
          <option value="2">2x</option>
        </SpeedControl>
      </VolumeControl>
    </PlayerContainer>
  );
};

export default SimpleAudioPlayerUI;