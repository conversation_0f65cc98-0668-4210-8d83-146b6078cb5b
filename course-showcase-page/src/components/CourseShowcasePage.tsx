import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { Header } from './Header';
import { TeacherIntroduction } from './TeacherIntroduction';
import { CoursePositioning } from './CoursePositioning';
import { CourseOutline } from './CourseOutline';
import { CallToAction } from './CallToAction';
import { AccessibilityValidator } from './AccessibilityValidator';
import { ResponsiveTestHelper } from './ResponsiveTestHelper';
import { LazyContent } from './LazyContent';
import { SkeletonLoading } from './LoadingStates';
import {
  AnimatedContainer,
  ScrollAnimationContainer,
  PageLoadingProgress,
  usePageTransition
} from './PageTransitions';

interface CourseShowcasePageProps {
  className?: string;
}

const PageContainer = styled.div`
  min-height: 100vh;
  background-color: ${({ theme }) => theme.colors.background};
  
  /* 为音频播放器预留空间 */
  padding-bottom: 120px;
  
  /* 移动端优化 */
  ${({ theme }) => theme.mediaQueries.mobile} {
    padding-bottom: calc(140px + env(safe-area-inset-bottom, 0));
  }
  
  /* 平板端 */
  ${({ theme }) => theme.mediaQueries.tablet} {
    padding-bottom: calc(120px + env(safe-area-inset-bottom, 0));
  }
  
  /* 桌面端不需要底部padding，因为播放器在侧边 */
  ${({ theme }) => theme.mediaQueries.desktop} {
    padding-bottom: 0;
  }
`;

const SkipLink = styled.a`
  position: relative;
  top: 0;
  left: 10px;
  background: ${({ theme }) => theme.colors.trustBlue};
  color: ${({ theme }) => theme.colors.white};
  padding: ${({ theme }) => theme.spacing[2]} ${({ theme }) => theme.spacing[4]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  text-decoration: none;
  font-weight: ${({ theme }) => theme.typography.fontWeights.medium};
  font-size: ${({ theme }) => theme.typography.fontSizes.sm};
  z-index: 10000;
  white-space: nowrap;
  
  &:focus {
    clip: auto;
    clip-path: none;
    height: auto;
    overflow: visible;
    position: relative;
    width: auto;
    outline: 3px solid ${({ theme }) => theme.colors.actionOrange};
    outline-offset: 2px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }
`;

const SkipLinksContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[1]};
  
  /* 确保跳转链接只在焦点时显示 */
  ${SkipLink}:not(:focus) {
    clip: rect(0 0 0 0);
    clip-path: inset(50%);
    height: 1px;
    overflow: hidden;
    position: absolute;
    white-space: nowrap;
    width: 1px;
  }
`;

const AdditionalSkipLink = styled(SkipLink)`
  /* 继承父组件的样式，无需额外定位 */
`;

const MainContent = styled.main`
  /* Course content sections */
  position: relative;
`;

const FloatingNavButton = styled.button`
  position: fixed;
  bottom: 100px;
  right: 20px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: ${({ theme }) => theme.colors.trustBlue};
  color: ${({ theme }) => theme.colors.white};
  border: none;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(106, 141, 175, 0.3);
  z-index: 999;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(106, 141, 175, 0.4);
  }
  
  &:focus {
    outline: 2px solid ${({ theme }) => theme.colors.actionOrange};
    outline-offset: 2px;
  }
  
  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    bottom: 80px;
    right: 16px;
    width: 44px;
    height: 44px;
    font-size: 18px;
  }
`;

// 左侧浮动导航菜单 - 靠上方浮动
const LeftFloatingNav = styled.nav`
  position: fixed;
  left: 0;
  top: 150px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 2px;
  
  /* 在移动端隐藏 */
  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    display: none;
  }
  
  /* 在平板端调整位置 */
  @media (min-width: ${({ theme }) => theme.breakpoints.tablet}) and (max-width: ${({ theme }) => theme.breakpoints.desktop}) {
    top: 120px;
  }
`;

const NavItem = styled.a`
  display: block;
  background: ${({ theme }) => theme.colors.trustBlue};
  color: ${({ theme }) => theme.colors.white};
  padding: ${({ theme }) => theme.spacing[4]} ${({ theme }) => theme.spacing[2]};
  text-decoration: none;
  font-family: ${({ theme }) => theme.typography.fonts.chinese};
  font-size: ${({ theme }) => theme.typography.fontSizes.xs};
  font-weight: ${({ theme }) => theme.typography.fontWeights.medium};
  writing-mode: vertical-rl;
  text-orientation: mixed;
  min-height: 100px;
  width: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0 ${({ theme }) => theme.borderRadius.lg} ${({ theme }) => theme.borderRadius.lg} 0;
  transition: all ${({ theme }) => theme.animations.duration.normal} ${({ theme }) => theme.animations.easing.easeOut};
  transform: translateX(-28px);
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  
  &:hover {
    transform: translateX(0);
    box-shadow: 4px 0 12px rgba(0, 0, 0, 0.15);
  }
  
  &:focus {
    transform: translateX(0);
    outline: 2px solid ${({ theme }) => theme.colors.actionOrange};
    outline-offset: 2px;
    box-shadow: 4px 0 12px rgba(0, 0, 0, 0.2);
  }
  
  /* 不同导航项使用不同颜色 */
  &:nth-child(1) {
    background: ${({ theme }) => theme.colors.trustBlue};
  }
  
  &:nth-child(2) {
    background: ${({ theme }) => theme.colors.growthGreen};
  }
  
  &:nth-child(3) {
    background: ${({ theme }) => theme.colors.actionOrange};
  }
  
  &:nth-child(4) {
    background: ${({ theme }) => theme.colors.trustBlue};
    opacity: 0.9;
  }
`;

export const CourseShowcasePage: React.FC<CourseShowcasePageProps> = ({ className }) => {
  const { isTransitioning, loadingProgress, startTransition } = usePageTransition();
  const [showScrollTop, setShowScrollTop] = useState(false);

  // 监听滚动，显示/隐藏回到顶部按钮
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // 页面加载时启动过渡动画
  useEffect(() => {
    startTransition();
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };



  return (
    <PageContainer className={className}>
      <PageLoadingProgress
        isLoading={isTransitioning}
        progress={loadingProgress}
      />

      {/* 无障碍跳转链接 - 暂时隐藏，以后再用 */}
      {false && (
        <SkipLinksContainer>
          <SkipLink href="#main-content" aria-label="跳转到主要内容">
            跳转到主要内容
          </SkipLink>
          <AdditionalSkipLink href="#teacher" aria-label="跳转到老师介绍">
            跳转到老师介绍
          </AdditionalSkipLink>
          <AdditionalSkipLink href="#outline" aria-label="跳转到课程大纲">
            跳转到课程大纲
          </AdditionalSkipLink>
          <AdditionalSkipLink href="#enrollment" aria-label="跳转到报名区域">
            跳转到报名区域
          </AdditionalSkipLink>
        </SkipLinksContainer>
      )}

      <AnimatedContainer animation="fadeInUp" delay={0.2}>
        <Header />
      </AnimatedContainer>

      <MainContent id="main-content" role="main" aria-label="课程展示页面主要内容">
        <ScrollAnimationContainer animation="slideInLeft" threshold={0.2}>
          <LazyContent
            placeholder={<SkeletonLoading lines={4} showAvatar={true} />}
            rootMargin="50px"
          >
            <TeacherIntroduction />
          </LazyContent>
        </ScrollAnimationContainer>

        <ScrollAnimationContainer animation="slideInRight" threshold={0.2}>
          <LazyContent
            placeholder={<SkeletonLoading lines={3} />}
            rootMargin="50px"
          >
            <CoursePositioning />
          </LazyContent>
        </ScrollAnimationContainer>

        <ScrollAnimationContainer animation="scaleIn" threshold={0.2}>
          <LazyContent
            placeholder={<SkeletonLoading lines={6} />}
            rootMargin="50px"
          >
            <CourseOutline />
          </LazyContent>
        </ScrollAnimationContainer>

        <ScrollAnimationContainer animation="fadeInUp" threshold={0.2}>
          <LazyContent
            placeholder={<SkeletonLoading lines={4} />}
            rootMargin="50px"
          >
            <CallToAction />
          </LazyContent>
        </ScrollAnimationContainer>
      </MainContent>

      {/* 左侧浮动导航菜单 - 暂时隐藏，以后再用 */}
      {false && (
        <LeftFloatingNav aria-label="页面导航">
          <NavItem href="#main-content" aria-label="跳转到主要内容">
            主要内容
          </NavItem>
          <NavItem href="#teacher" aria-label="跳转到老师介绍">
            老师介绍
          </NavItem>
          <NavItem href="#outline" aria-label="跳转到课程大纲">
            课程大纲
          </NavItem>
          <NavItem href="#enrollment" aria-label="跳转到报名区域">
            报名区域
          </NavItem>
        </LeftFloatingNav>
      )}

      {/* 浮动导航按钮 */}
      {showScrollTop && (
        <FloatingNavButton
          onClick={scrollToTop}
          aria-label="回到页面顶部"
          title="回到顶部"
        >
          ↑
        </FloatingNavButton>
      )}

      {/* Development tools - disabled */}
      {false && process.env.NODE_ENV === 'development' && (
        <>
          <AccessibilityValidator />
          <ResponsiveTestHelper />
        </>
      )}
    </PageContainer>
  );
};

export default CourseShowcasePage;