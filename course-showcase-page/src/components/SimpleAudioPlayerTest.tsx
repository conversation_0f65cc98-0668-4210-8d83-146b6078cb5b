import React from 'react';
import styled from 'styled-components';
import { useSimpleAudio } from '../hooks/useSimpleAudio';
import { getAllLessons } from '../data';

const TestContainer = styled.div`
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  border: 3px solid #007bff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  z-index: 9999;
  max-width: 600px;
  font-family: ${({ theme }) => theme.typography.fonts.chinese};
`;

const TestTitle = styled.h3`
  margin: 0 0 15px 0;
  color: #007bff;
  text-align: center;
`;

const StatusText = styled.div<{ $type: 'info' | 'success' | 'error' }>`
  padding: 10px;
  margin: 10px 0;
  border-radius: 4px;
  font-size: 14px;
  
  ${({ $type }) => {
    switch ($type) {
      case 'success':
        return 'background: #d4edda; color: #155724;';
      case 'error':
        return 'background: #f8d7da; color: #721c24;';
      default:
        return 'background: #d1ecf1; color: #0c5460;';
    }
  }}
`;

const ControlButton = styled.button`
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin: 4px;
  font-size: 14px;
  
  &:hover {
    background: #0056b3;
  }
  
  &:disabled {
    background: #ccc;
    cursor: not-allowed;
  }
`;

const LessonButton = styled.button`
  background: #28a745;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  margin: 2px;
  font-size: 12px;
  
  &:hover {
    background: #218838;
  }
`;

const AudioInfo = styled.div`
  font-size: 12px;
  color: #666;
  margin: 10px 0;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
`;

const CloseButton = styled.button`
  position: absolute;
  top: 10px;
  right: 10px;
  background: #6c757d;
  color: white;
  border: none;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 18px;
  
  &:hover {
    background: #5a6268;
  }
`;

export const SimpleAudioPlayerTest: React.FC = () => {
  const { state, playLesson, pauseAudio, resumeAudio } = useSimpleAudio();
  const [isVisible, setIsVisible] = React.useState(true);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getStatusType = (): 'info' | 'success' | 'error' => {
    if (state.error) return 'error';
    if (state.isPlaying) return 'success';
    return 'info';
  };

  const getStatusMessage = (): string => {
    if (state.error) return `错误: ${state.error}`;
    if (!state.currentLesson) return '请选择一个课程开始播放';
    if (state.isPlaying) return `正在播放: ${state.currentLesson.title}`;
    return `已暂停: ${state.currentLesson.title}`;
  };

  const lessons = getAllLessons().slice(0, 5); // 只显示前5个课程

  if (!isVisible) return null;

  return (
    <TestContainer>
      <CloseButton onClick={() => setIsVisible(false)}>×</CloseButton>
      
      <TestTitle>🎵 简化音频播放器测试</TestTitle>
      
      <StatusText $type={getStatusType()}>
        {getStatusMessage()}
      </StatusText>
      
      {state.currentLesson && (
        <AudioInfo>
          <div><strong>课程:</strong> {state.currentLesson.title}</div>
          <div><strong>音频:</strong> {state.currentLesson.audioUrl}</div>
          <div><strong>时长:</strong> {state.duration > 0 ? formatTime(state.duration) : '加载中...'}</div>
          <div><strong>进度:</strong> {formatTime(state.currentTime)} / {formatTime(state.duration)}</div>
        </AudioInfo>
      )}
      
      <div style={{ marginBottom: '15px' }}>
        {state.isPlaying ? (
          <ControlButton onClick={pauseAudio}>
            ⏸️ 暂停
          </ControlButton>
        ) : (
          <ControlButton onClick={resumeAudio} disabled={!state.currentLesson}>
            ▶️ 播放
          </ControlButton>
        )}
      </div>
      
      <div style={{ marginBottom: '10px' }}>
        <strong>测试课程:</strong>
      </div>
      <div>
        {lessons.map((lesson) => (
          <LessonButton
            key={lesson.id}
            onClick={() => playLesson(lesson)}
            style={{
              background: state.currentLesson?.id === lesson.id ? '#ffc107' : '#28a745',
              color: state.currentLesson?.id === lesson.id ? '#000' : '#fff'
            }}
          >
            {lesson.title.substring(0, 10)}...
          </LessonButton>
        ))}
      </div>
      
      <div style={{ marginTop: '10px', fontSize: '11px', color: '#666' }}>
        这是一个简化版的音频播放器，绕过了复杂的加载逻辑
      </div>
    </TestContainer>
  );
};

export default SimpleAudioPlayerTest;