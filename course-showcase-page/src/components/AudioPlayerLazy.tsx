import React, { Suspense, lazy } from 'react';
import styled from 'styled-components';
import ErrorBoundary from './ErrorBoundary';

// 懒加载音频播放器组件
const AudioPlayerComponent = lazy(() => 
  import('./AudioPlayer').then(module => ({ default: module.AudioPlayer }))
);

const LoadingContainer = styled.div`
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: ${({ theme }) => theme.colors.surface};
  border-top: 1px solid ${({ theme }) => theme.colors.border};
  box-shadow: ${({ theme }) => theme.shadows.lg};
  padding: ${({ theme }) => theme.spacing[4]};
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 80px;
  
  ${({ theme }) => theme.mediaQueries.desktop} {
    position: fixed;
    top: 50%;
    right: ${({ theme }) => theme.spacing[6]};
    bottom: auto;
    left: auto;
    width: 320px;
    transform: translateY(-50%);
    border-radius: ${({ theme }) => theme.borderRadius.xl};
    border: 1px solid ${({ theme }) => theme.colors.border};
  }
`;

const LoadingSpinner = styled.div`
  width: 24px;
  height: 24px;
  border: 2px solid ${({ theme }) => theme.colors.gray[300]};
  border-top: 2px solid ${({ theme }) => theme.colors.trustBlue};
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: ${({ theme }) => theme.spacing[3]};
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const LoadingText = styled.span`
  color: ${({ theme }) => theme.colors.gray[600]};
  font-size: ${({ theme }) => theme.typography.fontSizes.sm};
  font-family: ${({ theme }) => theme.typography.fonts.chinese};
`;

const AudioPlayerFallback = styled.div`
  background: ${({ theme }) => theme.colors.surface};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  padding: ${({ theme }) => theme.spacing[4]};
  text-align: center;
  color: ${({ theme }) => theme.colors.gray[600]};
  font-family: ${({ theme }) => theme.typography.fonts.chinese};
`;

// 加载中组件
const AudioPlayerLoading: React.FC = () => (
  <LoadingContainer role="status" aria-label="音频播放器加载中">
    <LoadingSpinner />
    <LoadingText>音频播放器加载中...</LoadingText>
  </LoadingContainer>
);

// 错误回退组件
const AudioPlayerError: React.FC = () => (
  <AudioPlayerFallback role="alert">
    <div style={{ marginBottom: '8px' }}>🎵</div>
    <div>音频播放器加载失败</div>
    <div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.7 }}>
      请刷新页面重试
    </div>
  </AudioPlayerFallback>
);

export const AudioPlayerLazy: React.FC = () => {
  return (
    <ErrorBoundary fallback={<AudioPlayerError />}>
      <Suspense fallback={<AudioPlayerLoading />}>
        <AudioPlayerComponent />
      </Suspense>
    </ErrorBoundary>
  );
};

export default AudioPlayerLazy;