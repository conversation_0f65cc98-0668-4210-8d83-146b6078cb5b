import React, { useState, useRef } from 'react';
import styled from 'styled-components';

const TestContainer = styled.div`
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  border: 3px solid #dc3545;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
  z-index: 10000;
  max-width: 500px;
  text-align: center;
`;

const TestButton = styled.button`
  background: #dc3545;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 4px;
  cursor: pointer;
  margin: 8px;
  font-size: 16px;
  
  &:hover {
    background: #c82333;
  }
  
  &:disabled {
    background: #ccc;
    cursor: not-allowed;
  }
`;

const StatusText = styled.div<{ $type: 'info' | 'success' | 'error' }>`
  padding: 12px;
  margin: 12px 0;
  border-radius: 4px;
  font-size: 14px;
  
  ${({ $type }) => {
    switch ($type) {
      case 'success':
        return 'background: #d4edda; color: #155724; border: 1px solid #c3e6cb;';
      case 'error':
        return 'background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb;';
      default:
        return 'background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb;';
    }
  }}
`;

const CloseButton = styled.button`
  position: absolute;
  top: 10px;
  right: 10px;
  background: #6c757d;
  color: white;
  border: none;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 18px;
  
  &:hover {
    background: #5a6268;
  }
`;

export const DirectAudioTest: React.FC = () => {
  const [status, setStatus] = useState<string>('准备直接测试音频播放');
  const [statusType, setStatusType] = useState<'info' | 'success' | 'error'>('info');
  const [isVisible, setIsVisible] = useState(true);
  const [isPlaying, setIsPlaying] = useState(false);
  const audioRef = useRef<HTMLAudioElement>(null);

  const updateStatus = (message: string, type: 'info' | 'success' | 'error' = 'info') => {
    setStatus(message);
    setStatusType(type);
    console.log(`[DirectAudioTest] ${message}`);
  };

  const testDirectPlay = async () => {
    try {
      updateStatus('开始直接播放测试...', 'info');
      
      // 创建新的音频元素
      const audio = new Audio('/audio/01.mp3');
      audioRef.current = audio;
      
      // 添加事件监听器
      audio.addEventListener('loadstart', () => {
        updateStatus('音频开始加载...', 'info');
      });
      
      audio.addEventListener('canplay', () => {
        updateStatus('音频可以播放，尝试播放...', 'success');
      });
      
      audio.addEventListener('playing', () => {
        updateStatus('音频正在播放！', 'success');
        setIsPlaying(true);
      });
      
      audio.addEventListener('pause', () => {
        updateStatus('音频已暂停', 'info');
        setIsPlaying(false);
      });
      
      audio.addEventListener('error', (e) => {
        const error = audio.error;
        let errorMsg = '播放错误';
        if (error) {
          switch (error.code) {
            case error.MEDIA_ERR_ABORTED:
              errorMsg = '播放被中止';
              break;
            case error.MEDIA_ERR_NETWORK:
              errorMsg = '网络错误';
              break;
            case error.MEDIA_ERR_DECODE:
              errorMsg = '解码错误';
              break;
            case error.MEDIA_ERR_SRC_NOT_SUPPORTED:
              errorMsg = '音频格式不支持';
              break;
            default:
              errorMsg = `未知错误 (${error.code})`;
          }
        }
        updateStatus(`播放失败: ${errorMsg}`, 'error');
        console.error('Audio error:', error, e);
      });
      
      // 尝试播放
      await audio.play();
      
      // 3秒后自动暂停
      setTimeout(() => {
        audio.pause();
        updateStatus('测试完成 - 音频播放正常！', 'success');
      }, 3000);
      
    } catch (error) {
      updateStatus(`播放失败: ${error instanceof Error ? error.message : '未知错误'}`, 'error');
      console.error('Play error:', error);
    }
  };

  const stopAudio = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      setIsPlaying(false);
      updateStatus('音频已停止', 'info');
    }
  };

  const testFileAccess = async () => {
    try {
      updateStatus('检查音频文件访问...', 'info');
      const response = await fetch('/audio/01.mp3', { method: 'HEAD' });
      
      if (response.ok) {
        const size = response.headers.get('content-length');
        const type = response.headers.get('content-type');
        updateStatus(`文件访问正常 - 大小: ${size ? Math.round(parseInt(size)/1024/1024*100)/100 + 'MB' : '未知'}, 类型: ${type}`, 'success');
      } else {
        updateStatus(`文件访问失败 - HTTP ${response.status}`, 'error');
      }
    } catch (error) {
      updateStatus(`文件检查失败: ${error instanceof Error ? error.message : '网络错误'}`, 'error');
    }
  };

  if (!isVisible) return null;

  return (
    <TestContainer>
      <CloseButton onClick={() => setIsVisible(false)}>×</CloseButton>
      
      <h3 style={{ margin: '0 0 20px 0', color: '#dc3545' }}>
        🎵 直接音频播放测试
      </h3>
      
      <StatusText $type={statusType}>
        {status}
      </StatusText>
      
      <div>
        <TestButton onClick={testFileAccess}>
          检查文件访问
        </TestButton>
        
        <TestButton onClick={testDirectPlay} disabled={isPlaying}>
          直接播放测试
        </TestButton>
        
        <TestButton onClick={stopAudio} disabled={!isPlaying}>
          停止播放
        </TestButton>
      </div>
      
      <div style={{ marginTop: '15px', fontSize: '12px', color: '#666' }}>
        这个测试绕过所有复杂逻辑，直接测试音频播放功能
      </div>
    </TestContainer>
  );
};

export default DirectAudioTest;