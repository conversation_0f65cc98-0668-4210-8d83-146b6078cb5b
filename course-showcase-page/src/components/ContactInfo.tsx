import React from 'react';
import styled from 'styled-components';
import { Typography, Container, QRCodeDisplay } from './ui';
import { useConfigContext } from '../contexts/ConfigContext';

interface ContactInfoProps {
  className?: string;
  showQRCodes?: boolean;
  layout?: 'horizontal' | 'vertical';
}

const ContactSection = styled.section`
  padding: ${({ theme }) => theme.spacing[12]} 0;
  background: linear-gradient(135deg, 
    ${({ theme }) => theme.colors.warmSand} 0%,
    ${({ theme }) => theme.colors.background} 100%
  );
`;

const ContactContainer = styled.div<{ layout: 'horizontal' | 'vertical' }>`
  display: flex;
  gap: ${({ theme }) => theme.spacing[8]};
  align-items: flex-start;
  
  ${({ layout, theme }) => layout === 'vertical' ? `
    flex-direction: column;
    align-items: center;
  ` : `
    flex-direction: row;
    justify-content: space-between;
    
    ${theme.mediaQueries.tablet} {
      flex-direction: column;
      align-items: center;
    }
  `}
`;

const ContactDetails = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[6]};
`;

const ContactItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
  padding: ${({ theme }) => theme.spacing[4]};
  background: ${({ theme }) => theme.colors.white};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all ${({ theme }) => theme.animations.duration.normal} ${({ theme }) => theme.animations.easing.easeOut};
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
`;

const ContactIcon = styled.div`
  width: 48px;
  height: 48px;
  border-radius: ${({ theme }) => theme.borderRadius.full};
  background: ${({ theme }) => theme.colors.actionOrange}20;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  flex-shrink: 0;
`;

const ContactText = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[1]};
`;

const ContactLabel = styled(Typography)`
  color: ${({ theme }) => theme.colors.gray[600]};
  font-size: ${({ theme }) => theme.typography.fontSizes.sm};
  font-weight: ${({ theme }) => theme.typography.fontWeights.medium};
`;

const ContactValue = styled(Typography)`
  color: ${({ theme }) => theme.colors.trustBlue};
  font-weight: ${({ theme }) => theme.typography.fontWeights.semibold};
  font-size: ${({ theme }) => theme.typography.fontSizes.lg};
`;

const QRCodesContainer = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[6]};
  flex-wrap: wrap;
  justify-content: center;
  
  ${({ theme }) => theme.mediaQueries.mobile} {
    flex-direction: column;
    align-items: center;
  }
`;

const SectionTitle = styled(Typography)`
  text-align: center;
  margin-bottom: ${({ theme }) => theme.spacing[8]};
  color: ${({ theme }) => theme.colors.trustBlue};
`;

export const ContactInfo: React.FC<ContactInfoProps> = ({
  className,
  showQRCodes = true,
  layout = 'horizontal'
}) => {
  const { businessConfig, isLoading } = useConfigContext();

  if (isLoading || !businessConfig) {
    return (
      <ContactSection className={className}>
        <Container>
          <SectionTitle variant="h2" chinese weight="bold">
            联系方式加载中...
          </SectionTitle>
        </Container>
      </ContactSection>
    );
  }

  const { contact } = businessConfig;

  return (
    <ContactSection className={className}>
      <Container>
        <SectionTitle variant="h2" chinese weight="bold">
          联系我们
        </SectionTitle>
        
        <ContactContainer layout={layout}>
          <ContactDetails>
            {contact.phone && (
              <ContactItem>
                <ContactIcon>📞</ContactIcon>
                <ContactText>
                  <ContactLabel variant="caption" chinese>
                    咨询电话
                  </ContactLabel>
                  <ContactValue variant="body" chinese>
                    {contact.phone}
                  </ContactValue>
                </ContactText>
              </ContactItem>
            )}
            
            {contact.email && (
              <ContactItem>
                <ContactIcon>📧</ContactIcon>
                <ContactText>
                  <ContactLabel variant="caption" chinese>
                    邮箱地址
                  </ContactLabel>
                  <ContactValue variant="body" chinese>
                    {contact.email}
                  </ContactValue>
                </ContactText>
              </ContactItem>
            )}
            
            {contact.wechat && (
              <ContactItem>
                <ContactIcon>💬</ContactIcon>
                <ContactText>
                  <ContactLabel variant="caption" chinese>
                    微信号
                  </ContactLabel>
                  <ContactValue variant="body" chinese>
                    {contact.wechat}
                  </ContactValue>
                </ContactText>
              </ContactItem>
            )}
          </ContactDetails>
          
          {showQRCodes && (
            <QRCodesContainer>
              {contact.wechatQrCode && (
                <QRCodeDisplay
                  qrCodeUrl={contact.wechatQrCode}
                  title="微信咨询"
                  description="扫码添加微信好友"
                  size="medium"
                />
              )}
              
              {contact.consultationQrCode && (
                <QRCodeDisplay
                  qrCodeUrl={contact.consultationQrCode}
                  title="免费咨询"
                  description="扫码获取课程详情"
                  size="medium"
                />
              )}
            </QRCodesContainer>
          )}
        </ContactContainer>
      </Container>
    </ContactSection>
  );
};
