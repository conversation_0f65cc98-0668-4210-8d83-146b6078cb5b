import React, { useState } from 'react';
import styled from 'styled-components';
import { LazyImage } from '../LazyImage';

interface QRCodeDisplayProps {
  qrCodeUrl: string;
  title: string;
  description?: string;
  className?: string;
  size?: 'small' | 'medium' | 'large';
}

const QRContainer = styled.div<{ size: 'small' | 'medium' | 'large' }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
  padding: ${({ theme }) => theme.spacing[4]};
  background: ${({ theme }) => theme.colors.white};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  border: 1px solid ${({ theme }) => theme.colors.gray[200]};
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all ${({ theme }) => theme.animations.duration.normal} ${({ theme }) => theme.animations.easing.easeOut};
  
  ${({ size }) => {
    switch (size) {
      case 'small':
        return `
          max-width: 200px;
        `;
      case 'large':
        return `
          max-width: 300px;
        `;
      default:
        return `
          max-width: 250px;
        `;
    }
  }}
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
`;

const QRImageContainer = styled.div<{ size: 'small' | 'medium' | 'large' }>`
  width: 100%;
  aspect-ratio: 1;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  overflow: hidden;
  background: ${({ theme }) => theme.colors.gray[50]};
  
  ${({ size }) => {
    switch (size) {
      case 'small':
        return `
          max-width: 120px;
        `;
      case 'large':
        return `
          max-width: 200px;
        `;
      default:
        return `
          max-width: 160px;
        `;
    }
  }}
`;

const QRTitle = styled.h3`
  margin: 0;
  font-size: ${({ theme }) => theme.typography.fontSizes.lg};
  font-weight: ${({ theme }) => theme.typography.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.trustBlue};
  text-align: center;
  font-family: ${({ theme }) => theme.typography.fonts.chinese};
`;

const QRDescription = styled.p`
  margin: 0;
  font-size: ${({ theme }) => theme.typography.fontSizes.sm};
  color: ${({ theme }) => theme.colors.gray[600]};
  text-align: center;
  line-height: ${({ theme }) => theme.typography.lineHeights.relaxed};
  font-family: ${({ theme }) => theme.typography.fonts.chinese};
`;

const QRPlaceholder = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: ${({ theme }) => theme.colors.gray[100]};
  color: ${({ theme }) => theme.colors.gray[500]};
  font-size: ${({ theme }) => theme.typography.fontSizes.sm};
  text-align: center;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const ErrorMessage = styled.div`
  color: ${({ theme }) => theme.colors.gray[500]};
  font-size: ${({ theme }) => theme.typography.fontSizes.sm};
  text-align: center;
  padding: ${({ theme }) => theme.spacing[4]};
`;

export const QRCodeDisplay: React.FC<QRCodeDisplayProps> = ({
  qrCodeUrl,
  title,
  description,
  className,
  size = 'medium'
}) => {
  const [imageError, setImageError] = useState(false);

  const handleImageError = () => {
    setImageError(true);
  };

  return (
    <QRContainer className={className} size={size}>
      <QRImageContainer size={size}>
        {imageError ? (
          <QRPlaceholder>
            <div>📱</div>
            <div>二维码加载失败</div>
          </QRPlaceholder>
        ) : (
          <LazyImage
            src={qrCodeUrl}
            alt={`${title}二维码`}
            placeholder={
              <QRPlaceholder>
                <div>📱</div>
                <div>二维码加载中...</div>
              </QRPlaceholder>
            }
            onError={handleImageError}
            style={{ width: '100%', height: '100%', objectFit: 'cover' }}
          />
        )}
      </QRImageContainer>
      
      <QRTitle>{title}</QRTitle>
      
      {description && (
        <QRDescription>{description}</QRDescription>
      )}
    </QRContainer>
  );
};
