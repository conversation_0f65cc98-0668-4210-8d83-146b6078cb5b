import styled, { css } from 'styled-components';

// Typography variants
type TypographyVariant = 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'body' | 'caption' | 'overline';
type TypographyWeight = 'light' | 'normal' | 'medium' | 'semibold' | 'bold';
type TypographyAlign = 'left' | 'center' | 'right';

interface TypographyProps {
  variant?: TypographyVariant;
  weight?: TypographyWeight;
  align?: TypographyAlign;
  color?: string;
  chinese?: boolean;
  className?: string;
  style?: React.CSSProperties;
  children: React.ReactNode;
}

// Base typography styles
const getVariantStyles = (variant: TypographyVariant) => {
  const variants = {
    h1: css`
      font-size: ${({ theme }) => theme.typography.fontSizes['5xl']};
      line-height: ${({ theme }) => theme.typography.lineHeights.tight};
      font-weight: ${({ theme }) => theme.typography.fontWeights.bold};
    `,
    h2: css`
      font-size: ${({ theme }) => theme.typography.fontSizes['4xl']};
      line-height: ${({ theme }) => theme.typography.lineHeights.tight};
      font-weight: ${({ theme }) => theme.typography.fontWeights.bold};
    `,
    h3: css`
      font-size: ${({ theme }) => theme.typography.fontSizes['3xl']};
      line-height: ${({ theme }) => theme.typography.lineHeights.tight};
      font-weight: ${({ theme }) => theme.typography.fontWeights.semibold};
    `,
    h4: css`
      font-size: ${({ theme }) => theme.typography.fontSizes['2xl']};
      line-height: ${({ theme }) => theme.typography.lineHeights.normal};
      font-weight: ${({ theme }) => theme.typography.fontWeights.semibold};
    `,
    h5: css`
      font-size: ${({ theme }) => theme.typography.fontSizes.xl};
      line-height: ${({ theme }) => theme.typography.lineHeights.normal};
      font-weight: ${({ theme }) => theme.typography.fontWeights.medium};
    `,
    h6: css`
      font-size: ${({ theme }) => theme.typography.fontSizes.lg};
      line-height: ${({ theme }) => theme.typography.lineHeights.normal};
      font-weight: ${({ theme }) => theme.typography.fontWeights.medium};
    `,
    body: css`
      font-size: ${({ theme }) => theme.typography.fontSizes.base};
      line-height: ${({ theme }) => theme.typography.lineHeights.normal};
      font-weight: ${({ theme }) => theme.typography.fontWeights.normal};
    `,
    caption: css`
      font-size: ${({ theme }) => theme.typography.fontSizes.sm};
      line-height: ${({ theme }) => theme.typography.lineHeights.normal};
      font-weight: ${({ theme }) => theme.typography.fontWeights.normal};
    `,
    overline: css`
      font-size: ${({ theme }) => theme.typography.fontSizes.xs};
      line-height: ${({ theme }) => theme.typography.lineHeights.normal};
      font-weight: ${({ theme }) => theme.typography.fontWeights.medium};
      text-transform: uppercase;
      letter-spacing: 0.1em;
    `,
  };
  
  return variants[variant];
};

const StyledTypography = styled.div.withConfig({
  shouldForwardProp: (prop) => !['chinese', 'variant', 'weight', 'align'].includes(prop)
})<TypographyProps>`
  margin: 0;
  padding: 0;
  
  /* Font family based on language */
  font-family: ${({ chinese, theme }) => 
    chinese ? theme.typography.fonts.chinese : theme.typography.fonts.english
  };
  
  /* Variant styles */
  ${({ variant = 'body' }) => getVariantStyles(variant)}
  
  /* Weight override */
  ${({ weight, theme }) => weight && css`
    font-weight: ${theme.typography.fontWeights[weight]};
  `}
  
  /* Text alignment */
  ${({ align }) => align && css`
    text-align: ${align};
  `}
  
  /* Color override */
  ${({ color }) => color && css`
    color: ${color};
  `}
  
  /* Responsive font sizes - Mobile first approach */
  @media (max-width: 767px) {
    ${({ variant }) => variant === 'h1' && css`
      font-size: ${({ theme }) => theme.typography.fontSizes['3xl']};
      line-height: 1.2;
    `}
    ${({ variant }) => variant === 'h2' && css`
      font-size: ${({ theme }) => theme.typography.fontSizes['2xl']};
      line-height: 1.3;
    `}
    ${({ variant }) => variant === 'h3' && css`
      font-size: ${({ theme }) => theme.typography.fontSizes.xl};
      line-height: 1.3;
    `}
    ${({ variant }) => variant === 'h4' && css`
      font-size: ${({ theme }) => theme.typography.fontSizes.lg};
      line-height: 1.4;
    `}
  }
  
  /* Tablet adjustments */
  ${({ theme }) => theme.mediaQueries.tablet} {
    ${({ variant }) => variant === 'h1' && css`
      font-size: ${({ theme }) => theme.typography.fontSizes['4xl']};
    `}
    ${({ variant }) => variant === 'h2' && css`
      font-size: ${({ theme }) => theme.typography.fontSizes['3xl']};
    `}
  }
`;

// Component mapping for semantic HTML
const getComponent = (variant: TypographyVariant) => {
  const componentMap = {
    h1: 'h1',
    h2: 'h2', 
    h3: 'h3',
    h4: 'h4',
    h5: 'h5',
    h6: 'h6',
    body: 'p',
    caption: 'span',
    overline: 'span',
  };
  
  return componentMap[variant];
};

export const Typography: React.FC<TypographyProps> = ({ 
  variant = 'body', 
  children, 
  ...props 
}) => {
  return (
    <StyledTypography 
      as={getComponent(variant)}
      variant={variant}
      {...props}
    >
      {children}
    </StyledTypography>
  );
};

export default Typography;