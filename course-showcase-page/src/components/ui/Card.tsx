import styled, { css } from 'styled-components';

interface CardProps {
  variant?: 'default' | 'elevated' | 'outlined';
  padding?: 'sm' | 'md' | 'lg';
  hover?: boolean;
  className?: string;
  children: React.ReactNode;
  onClick?: () => void;
}

const StyledCard = styled.div<CardProps>`
  background-color: ${({ theme }) => theme.colors.white};
  border-radius: ${({ theme }) => theme.borderRadius.xl};
  transition: all ${({ theme }) => theme.animations.duration.normal} ${({ theme }) => theme.animations.easing.easeOut};
  
  /* Variant styles */
  ${({ variant = 'default', theme }) => {
    switch (variant) {
      case 'elevated':
        return css`
          box-shadow: ${theme.shadows.base};
        `;
      case 'outlined':
        return css`
          border: 1px solid ${theme.colors.gray[200]};
        `;
      default:
        return css`
          box-shadow: ${theme.shadows.sm};
        `;
    }
  }}
  
  /* Padding */
  ${({ padding = 'md', theme }) => {
    const paddingMap = {
      sm: theme.spacing[4],
      md: theme.spacing[6],
      lg: theme.spacing[8],
    };
    
    return css`
      padding: ${paddingMap[padding]};
    `;
  }}
  
  /* Hover effects */
  ${({ hover, theme }) => hover && css`
    cursor: pointer;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: ${theme.shadows.md};
    }
    
    &:active {
      transform: translateY(0);
    }
  `}
  
  /* Focus styles for accessibility */
  ${({ onClick, theme }) => onClick && css`
    &:focus {
      outline: 2px solid ${theme.colors.trustBlue};
      outline-offset: 2px;
    }
  `}
`;

export const Card: React.FC<CardProps> = ({ children, onClick, ...props }) => {
  return (
    <StyledCard 
      {...props}
      onClick={onClick}
      tabIndex={onClick ? 0 : undefined}
      role={onClick ? 'button' : undefined}
    >
      {children}
    </StyledCard>
  );
};

export default Card;