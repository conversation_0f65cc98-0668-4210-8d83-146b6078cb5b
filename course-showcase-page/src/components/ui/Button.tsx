import styled, { css } from 'styled-components';

interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  disabled?: boolean;
  loading?: boolean;
  className?: string;
  children: React.ReactNode;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
}

const StyledButton = styled.button<ButtonProps>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  font-family: ${({ theme }) => theme.typography.fonts.chinese};
  font-weight: ${({ theme }) => theme.typography.fontWeights.medium};
  text-decoration: none;
  cursor: pointer;
  transition: all ${({ theme }) => theme.animations.duration.normal} ${({ theme }) => theme.animations.easing.easeOut};
  
  /* Ensure minimum touch target size */
  min-height: ${({ theme }) => theme.touchTargets.minimum};
  min-width: ${({ theme }) => theme.touchTargets.minimum};
  
  /* Size variants */
  ${({ size = 'md', theme }) => {
    const sizeMap = {
      sm: css`
        padding: ${theme.spacing[2]} ${theme.spacing[4]};
        font-size: ${theme.typography.fontSizes.sm};
      `,
      md: css`
        padding: ${theme.spacing[3]} ${theme.spacing[6]};
        font-size: ${theme.typography.fontSizes.base};
      `,
      lg: css`
        padding: ${theme.spacing[4]} ${theme.spacing[8]};
        font-size: ${theme.typography.fontSizes.lg};
      `,
    };
    
    return sizeMap[size];
  }}
  
  /* Color variants */
  ${({ variant = 'primary', theme }) => {
    switch (variant) {
      case 'primary':
        return css`
          background-color: ${theme.colors.trustBlue};
          color: ${theme.colors.white};
          
          &:hover:not(:disabled) {
            background-color: ${theme.colors.gray[700]};
            transform: translateY(-1px);
            box-shadow: ${theme.shadows.md};
          }
          
          &:active:not(:disabled) {
            transform: translateY(0);
          }
        `;
      case 'secondary':
        return css`
          background-color: ${theme.colors.growthGreen};
          color: ${theme.colors.white};
          
          &:hover:not(:disabled) {
            background-color: ${theme.colors.gray[600]};
            transform: translateY(-1px);
            box-shadow: ${theme.shadows.md};
          }
        `;
      case 'outline':
        return css`
          background-color: transparent;
          color: ${theme.colors.trustBlue};
          border: 2px solid ${theme.colors.trustBlue};
          
          &:hover:not(:disabled) {
            background-color: ${theme.colors.trustBlue};
            color: ${theme.colors.white};
          }
        `;
      case 'ghost':
        return css`
          background-color: transparent;
          color: ${theme.colors.trustBlue};
          
          &:hover:not(:disabled) {
            background-color: ${theme.colors.gray[100]};
          }
        `;
    }
  }}
  
  /* Full width */
  ${({ fullWidth }) => fullWidth && css`
    width: 100%;
  `}
  
  /* Disabled state */
  ${({ disabled }) => disabled && css`
    opacity: 0.6;
    cursor: not-allowed;
    
    &:hover {
      transform: none;
      box-shadow: none;
    }
  `}
  
  /* Loading state */
  ${({ loading }) => loading && css`
    cursor: wait;
    opacity: 0.8;
  `}
  
  /* Focus styles for accessibility */
  &:focus {
    outline: 2px solid ${({ theme }) => theme.colors.trustBlue};
    outline-offset: 2px;
  }
  
  /* Active state */
  &:active:not(:disabled) {
    transform: scale(0.98);
  }
`;

export const Button: React.FC<ButtonProps> = ({ 
  children, 
  type = 'button',
  disabled,
  loading,
  ...props 
}) => {
  return (
    <StyledButton 
      type={type}
      disabled={disabled || loading}
      loading={loading}
      {...props}
    >
      {loading ? 'Loading...' : children}
    </StyledButton>
  );
};

export default Button;