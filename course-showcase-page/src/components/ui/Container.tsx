import styled, { css } from 'styled-components';

interface ContainerProps {
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  padding?: boolean;
  center?: boolean;
  className?: string;
  children: React.ReactNode;
}

const maxWidths = {
  sm: '640px',
  md: '768px', 
  lg: '1024px',
  xl: '1280px',
  full: '100%',
};

const StyledContainer = styled.div<ContainerProps>`
  width: 100%;
  
  /* Max width */
  max-width: ${({ maxWidth = 'lg' }) => maxWidths[maxWidth]};
  
  /* Centering */
  ${({ center = true }) => center && css`
    margin-left: auto;
    margin-right: auto;
  `}
  
  /* Responsive padding - Mobile first approach */
  ${({ padding = true, theme }) => padding && css`
    /* Mobile: 16px sides */
    padding-left: ${theme.spacing[4]};
    padding-right: ${theme.spacing[4]};
    
    /* Tablet: 24px sides */
    ${theme.mediaQueries.tablet} {
      padding-left: ${theme.spacing[6]};
      padding-right: ${theme.spacing[6]};
    }
    
    /* Desktop: 32px sides */
    ${theme.mediaQueries.desktop} {
      padding-left: ${theme.spacing[8]};
      padding-right: ${theme.spacing[8]};
    }
    
    /* Wide screens: max padding */
    @media (min-width: 1440px) {
      padding-left: ${theme.spacing[12]};
      padding-right: ${theme.spacing[12]};
    }
  `}
`;

export const Container: React.FC<ContainerProps> = ({ children, ...props }) => {
  return (
    <StyledContainer {...props}>
      {children}
    </StyledContainer>
  );
};

export default Container;