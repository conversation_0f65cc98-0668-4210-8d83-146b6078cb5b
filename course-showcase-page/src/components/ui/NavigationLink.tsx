import React from 'react';
import styled from 'styled-components';
import type { NavigationItem } from '../../types';

interface NavigationLinkProps extends NavigationItem {
  className?: string;
  children?: React.ReactNode;
  role?: string;
  'aria-label'?: string;
  onClick?: (e: React.MouseEvent) => void;
}

const StyledLink = styled.a`
  text-decoration: none;
  color: inherit;
  cursor: pointer;
  
  &:hover {
    opacity: 0.8;
    transition: opacity 0.2s ease;
  }
  
  &:focus {
    outline: 2px solid ${({ theme }) => theme.colors.actionOrange};
    outline-offset: 2px;
    border-radius: 4px;
  }
`;

export const NavigationLink: React.FC<NavigationLinkProps> = ({
  text,
  url,
  openInNewWindow,
  className,
  children,
  role,
  'aria-label': ariaLabel,
  onClick,
  ...props
}) => {
  const handleClick = (e: React.MouseEvent) => {
    if (onClick) {
      onClick(e);
    }
    
    // Handle internal anchor links
    if (url.startsWith('#')) {
      e.preventDefault();
      const element = document.querySelector(url);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    }
  };

  const linkProps = {
    href: url,
    className,
    role,
    'aria-label': ariaLabel || text,
    onClick: handleClick,
    ...(openInNewWindow && {
      target: '_blank',
      rel: 'noopener noreferrer'
    }),
    ...props
  };

  return (
    <StyledLink {...linkProps}>
      {children || text}
    </StyledLink>
  );
};
