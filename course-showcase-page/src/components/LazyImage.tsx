import React, { useState, useRef, useEffect } from 'react';
import styled, { keyframes } from 'styled-components';

import type { ReactNode } from 'react';

interface LazyImageProps {
  src: string;
  alt: string;
  placeholder?: ReactNode;
  className?: string;
  width?: number | string;
  height?: number | string;
  loading?: 'lazy' | 'eager';
  onLoad?: () => void;
  onError?: () => void;
}

const shimmer = keyframes`
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
`;

const ImageContainer = styled.div<{ $width?: number | string; $height?: number | string }>`
  position: relative;
  overflow: hidden;
  width: ${({ $width }) => typeof $width === 'number' ? `${$width}px` : $width || '100%'};
  height: ${({ $height }) => typeof $height === 'number' ? `${$height}px` : $height || 'auto'};
`;

const PlaceholderContainer = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(
    90deg,
    ${({ theme }) => theme.colors.gray[200]} 0%,
    ${({ theme }) => theme.colors.gray[100]} 50%,
    ${({ theme }) => theme.colors.gray[200]} 100%
  );
  background-size: 200px 100%;
  animation: ${shimmer} 1.5s ease-in-out infinite;
  color: ${({ theme }) => theme.colors.gray[500]};
  font-size: ${({ theme }) => theme.typography.fontSizes.sm};
`;

const Image = styled.img<{ $loaded: boolean }>`
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity ${({ theme }) => theme.animations.duration.normal} 
    ${({ theme }) => theme.animations.easing.easeOut};
  opacity: ${({ $loaded }) => ($loaded ? 1 : 0)};
`;

const ErrorPlaceholder = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: ${({ theme }) => theme.colors.gray[100]};
  color: ${({ theme }) => theme.colors.gray[500]};
  font-size: ${({ theme }) => theme.typography.fontSizes.sm};
  text-align: center;
  padding: ${({ theme }) => theme.spacing[4]};
`;

export const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt,
  placeholder,
  className,
  width,
  height,
  loading = 'lazy',
  onLoad,
  onError
}) => {
  const [loaded, setLoaded] = useState(false);
  const [error, setError] = useState(false);
  const [inView, setInView] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (loading === 'eager') {
      setInView(true);
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setInView(true);
          observer.disconnect();
        }
      },
      {
        rootMargin: '50px', // 提前50px开始加载
        threshold: 0.1
      }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => observer.disconnect();
  }, [loading]);

  const handleLoad = () => {
    setLoaded(true);
    setError(false);
    onLoad?.();
  };

  const handleError = () => {
    setError(true);
    setLoaded(false);
    onError?.();
  };

  return (
    <ImageContainer
      ref={containerRef}
      className={className}
      $width={width}
      $height={height}
      role="img"
      aria-label={alt}
    >
      {!loaded && !error && (
        <PlaceholderContainer>
          {placeholder || <span>加载中...</span>}
        </PlaceholderContainer>
      )}
      
      {error && (
        <ErrorPlaceholder role="alert">
          <div style={{ fontSize: '24px', marginBottom: '8px' }}>📷</div>
          <div>图片加载失败</div>
        </ErrorPlaceholder>
      )}
      
      {inView && (
        <Image
          ref={imgRef}
          src={src}
          alt={alt}
          $loaded={loaded}
          onLoad={handleLoad}
          onError={handleError}
          loading={loading}
        />
      )}
    </ImageContainer>
  );
};

export default LazyImage;