import React, { useState, useRef } from 'react';
import styled from 'styled-components';

const TestContainer = styled.div`
  position: fixed;
  top: 20px;
  right: 20px;
  background: white;
  border: 2px solid #007bff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  z-index: 9999;
  max-width: 300px;
`;

const TestButton = styled.button`
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin: 4px;
  
  &:hover {
    background: #0056b3;
  }
  
  &:disabled {
    background: #ccc;
    cursor: not-allowed;
  }
`;

const StatusText = styled.div<{ $type: 'info' | 'success' | 'error' }>`
  padding: 8px;
  margin: 8px 0;
  border-radius: 4px;
  font-size: 12px;
  
  ${({ $type }) => {
    switch ($type) {
      case 'success':
        return 'background: #d4edda; color: #155724;';
      case 'error':
        return 'background: #f8d7da; color: #721c24;';
      default:
        return 'background: #d1ecf1; color: #0c5460;';
    }
  }}
`;

export const SimpleAudioTest: React.FC = () => {
  const [status, setStatus] = useState<string>('准备测试');
  const [statusType, setStatusType] = useState<'info' | 'success' | 'error'>('info');
  const [isPlaying, setIsPlaying] = useState(false);
  const audioRef = useRef<HTMLAudioElement>(null);

  const updateStatus = (message: string, type: 'info' | 'success' | 'error' = 'info') => {
    setStatus(message);
    setStatusType(type);
    console.log(`[AudioTest] ${message}`);
  };

  const testAudio = async (audioFile: string) => {
    const audio = audioRef.current;
    if (!audio) {
      updateStatus('音频元素未找到', 'error');
      return;
    }

    try {
      updateStatus(`正在测试 ${audioFile}...`, 'info');
      
      // 设置音频源
      audio.src = `/audio/${audioFile}`;
      
      // 添加事件监听器
      const handleLoadedMetadata = () => {
        updateStatus(`音频加载成功，时长: ${Math.round(audio.duration)}秒`, 'success');
      };
      
      const handleError = () => {
        updateStatus(`音频加载失败: ${audio.error?.message || '未知错误'}`, 'error');
      };
      
      const handleCanPlay = () => {
        updateStatus('音频可以播放', 'success');
      };

      audio.addEventListener('loadedmetadata', handleLoadedMetadata);
      audio.addEventListener('error', handleError);
      audio.addEventListener('canplay', handleCanPlay);
      
      // 加载音频
      audio.load();
      
      // 清理事件监听器
      setTimeout(() => {
        audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
        audio.removeEventListener('error', handleError);
        audio.removeEventListener('canplay', handleCanPlay);
      }, 5000);
      
    } catch (error) {
      updateStatus(`测试失败: ${error instanceof Error ? error.message : '未知错误'}`, 'error');
    }
  };

  const playAudio = async () => {
    const audio = audioRef.current;
    if (!audio) return;

    try {
      if (isPlaying) {
        audio.pause();
        setIsPlaying(false);
        updateStatus('音频已暂停', 'info');
      } else {
        await audio.play();
        setIsPlaying(true);
        updateStatus('音频播放中...', 'success');
        
        // 播放3秒后自动暂停
        setTimeout(() => {
          audio.pause();
          setIsPlaying(false);
          updateStatus('测试播放完成', 'success');
        }, 3000);
      }
    } catch (error) {
      updateStatus(`播放失败: ${error instanceof Error ? error.message : '未知错误'}`, 'error');
      setIsPlaying(false);
    }
  };

  const checkUrl = async (audioFile: string) => {
    try {
      updateStatus(`检查文件 ${audioFile}...`, 'info');
      const response = await fetch(`/audio/${audioFile}`, { method: 'HEAD' });
      
      if (response.ok) {
        const contentLength = response.headers.get('content-length');
        const contentType = response.headers.get('content-type');
        updateStatus(
          `文件存在 - 大小: ${contentLength ? Math.round(parseInt(contentLength)/1024/1024*100)/100 + 'MB' : '未知'}, 类型: ${contentType || '未知'}`, 
          'success'
        );
      } else {
        updateStatus(`文件不存在 (HTTP ${response.status})`, 'error');
      }
    } catch (error) {
      updateStatus(`检查失败: ${error instanceof Error ? error.message : '网络错误'}`, 'error');
    }
  };

  return (
    <TestContainer>
      <h4>🎵 音频测试工具</h4>
      
      <StatusText $type={statusType}>
        {status}
      </StatusText>
      
      <div>
        <TestButton onClick={() => checkUrl('01.mp3')}>
          检查 01.mp3
        </TestButton>
        <TestButton onClick={() => testAudio('01.mp3')}>
          加载 01.mp3
        </TestButton>
        <TestButton onClick={playAudio} disabled={!audioRef.current?.src}>
          {isPlaying ? '暂停' : '播放'}
        </TestButton>
      </div>
      
      <div>
        <TestButton onClick={() => checkUrl('02.mp3')}>
          检查 02.mp3
        </TestButton>
        <TestButton onClick={() => testAudio('02.mp3')}>
          加载 02.mp3
        </TestButton>
      </div>
      
      <audio
        ref={audioRef}
        style={{ display: 'none' }}
        preload="metadata"
      />
    </TestContainer>
  );
};

export default SimpleAudioTest;