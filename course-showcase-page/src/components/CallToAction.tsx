import React, { useState } from 'react';
import styled, { keyframes } from 'styled-components';
import { Button } from './ui/Button';
import { Card } from './ui/Card';
import { Typography } from './ui/Typography';
import { NavigationLink } from './ui/NavigationLink';
import { useConfigContext } from '../contexts/ConfigContext';

const pulseAnimation = keyframes`
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(247, 183, 135, 0.7);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(247, 183, 135, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(247, 183, 135, 0);
  }
`;

const fadeInUp = keyframes`
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

const CTASection = styled.section`
  padding: ${({ theme }) => theme.spacing[16]} ${({ theme }) => theme.spacing[4]};
  background: linear-gradient(135deg, 
    ${({ theme }) => theme.colors.background} 0%,
    ${({ theme }) => theme.colors.warmSand} 50%,
    ${({ theme }) => theme.colors.growthGreen}20 100%
  );
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, 
      ${({ theme }) => theme.colors.actionOrange}10 0%,
      transparent 70%
    );
    animation: ${fadeInUp} 1s ease-out;
  }
  
  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    padding: ${({ theme }) => theme.spacing[12]} ${({ theme }) => theme.spacing[4]};
  }
`;

const CTAContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
  position: relative;
  z-index: 1;
`;

const CTACard = styled(Card)`
  padding: ${({ theme }) => theme.spacing[8]};
  background: ${({ theme }) => theme.colors.white};
  border: 2px solid ${({ theme }) => theme.colors.actionOrange}30;
  animation: ${fadeInUp} 0.8s ease-out;
  
  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    padding: ${({ theme }) => theme.spacing[6]};
  }
`;

const CTATitle = styled(Typography)`
  margin-bottom: ${({ theme }) => theme.spacing[4]};
  color: ${({ theme }) => theme.colors.trustBlue};
  font-weight: ${({ theme }) => theme.typography.fontWeights.bold};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    font-size: ${({ theme }) => theme.typography.fontSizes['2xl']};
  }
`;

const CTADescription = styled(Typography)`
  margin-bottom: ${({ theme }) => theme.spacing[6]};
  color: ${({ theme }) => theme.colors.gray[700]};
  line-height: 1.6;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
`;

const CTAButtonContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[4]};
  align-items: center;
  
  @media (min-width: ${({ theme }) => theme.breakpoints.tablet}) {
    flex-direction: row;
    justify-content: center;
  }
`;

const PrimaryButton = styled(Button)`
  animation: ${pulseAnimation} 2s infinite;
  font-family: ${({ theme }) => theme.typography.fonts.chinese};
  font-size: ${({ theme }) => theme.typography.fontSizes.lg};
  padding: ${({ theme }) => theme.spacing[4]} ${({ theme }) => theme.spacing[8]};
  min-width: 200px;
  
  &:hover {
    animation: none;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(247, 183, 135, 0.3);
  }
  
  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    width: 100%;
    max-width: 300px;
  }
`;

const SecondaryButton = styled(Button)`
  background: transparent;
  color: ${({ theme }) => theme.colors.trustBlue};
  border: 2px solid ${({ theme }) => theme.colors.trustBlue};
  font-family: ${({ theme }) => theme.typography.fonts.chinese};
  min-width: 200px;
  
  &:hover {
    background: ${({ theme }) => theme.colors.trustBlue};
    color: ${({ theme }) => theme.colors.white};
    transform: translateY(-2px);
  }
  
  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    width: 100%;
    max-width: 300px;
  }
`;

const PriceInfo = styled.div`
  margin-top: ${({ theme }) => theme.spacing[6]};
  padding: ${({ theme }) => theme.spacing[4]};
  background: ${({ theme }) => theme.colors.growthGreen}20;
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  border-left: 4px solid ${({ theme }) => theme.colors.growthGreen};
`;

const PriceText = styled(Typography)`
  color: ${({ theme }) => theme.colors.trustBlue};
  font-weight: ${({ theme }) => theme.typography.fontWeights.semibold};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
`;

const LimitedTimeText = styled(Typography)`
  color: ${({ theme }) => theme.colors.actionOrange};
  font-weight: ${({ theme }) => theme.typography.fontWeights.bold};
  font-size: ${({ theme }) => theme.typography.fontSizes.sm};
`;

const TrustIndicators = styled.div`
  display: flex;
  justify-content: center;
  gap: ${({ theme }) => theme.spacing[6]};
  margin-top: ${({ theme }) => theme.spacing[6]};
  flex-wrap: wrap;
`;

const TrustItem = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const TrustNumber = styled(Typography)`
  font-size: ${({ theme }) => theme.typography.fontSizes['2xl']};
  font-weight: ${({ theme }) => theme.typography.fontWeights.bold};
  color: ${({ theme }) => theme.colors.actionOrange};
`;

const TrustLabel = styled(Typography)`
  font-size: ${({ theme }) => theme.typography.fontSizes.sm};
  color: ${({ theme }) => theme.colors.gray[600]};
`;

interface CallToActionProps {
  className?: string;
}

export const CallToAction: React.FC<CallToActionProps> = ({ className }) => {
  const [isEnrolling, setIsEnrolling] = useState(false);
  const { businessConfig, basicConfig, isLoading } = useConfigContext();

  const handleEnrollment = async () => {
    setIsEnrolling(true);

    // 模拟注册流程
    try {
      // 这里可以集成实际的注册API
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 成功后的处理
      alert('注册成功！我们将很快与您联系。');
    } catch (error) {
      alert('注册过程中出现问题，请稍后重试。');
    } finally {
      setIsEnrolling(false);
    }
  };

  // Show loading state if config is not ready
  if (isLoading || !businessConfig || !basicConfig) {
    return (
      <CTASection className={className} id="enrollment">
        <CTAContainer>
          <CTACard>
            <CTATitle as="h2" variant="h2" chinese>
              加载中...
            </CTATitle>
          </CTACard>
        </CTAContainer>
      </CTASection>
    );
  }

  return (
    <CTASection className={className} id="enrollment" aria-labelledby="cta-title">
      <CTAContainer>
        <CTACard>
          <CTATitle as="h2" variant="h2" chinese id="cta-title">
            {businessConfig.cta.title}
          </CTATitle>

          <CTADescription variant="h6" chinese>
            {businessConfig.cta.description}
          </CTADescription>

          <CTAButtonContainer>
            <NavigationLink
              {...businessConfig.cta.primaryButton}
              onClick={businessConfig.cta.primaryButton.url.startsWith('http') ? undefined : handleEnrollment}
            >
              <PrimaryButton
                disabled={isEnrolling}
                aria-label={`${businessConfig.cta.primaryButton.text} - ${businessConfig.cta.primaryButton.url.startsWith('http') ? '跳转到外部链接' : '内部处理'}`}
              >
                {isEnrolling && !businessConfig.cta.primaryButton.url.startsWith('http')
                  ? basicConfig.buttons.processing
                  : businessConfig.cta.primaryButton.text}
              </PrimaryButton>
            </NavigationLink>

            <NavigationLink {...businessConfig.cta.secondaryButton}>
              <SecondaryButton
                aria-label={`${businessConfig.cta.secondaryButton.text} - ${businessConfig.cta.secondaryButton.url.startsWith('http') ? '跳转到外部链接' : '内部处理'}`}
              >
                {businessConfig.cta.secondaryButton.text}
              </SecondaryButton>
            </NavigationLink>
          </CTAButtonContainer>

          <PriceInfo>
            <PriceText variant="h6" chinese>
              {businessConfig.pricing.priceLabel}：{businessConfig.pricing.currentPrice}（原价 {businessConfig.pricing.originalPrice}）
            </PriceText>
            <LimitedTimeText variant="caption" chinese>
              {businessConfig.pricing.limitedOffer}
            </LimitedTimeText>
          </PriceInfo>

          <TrustIndicators>
            <TrustItem>
              <TrustNumber variant="h3" chinese>5000+</TrustNumber>
              <TrustLabel variant="caption" chinese>服务家庭</TrustLabel>
            </TrustItem>
            <TrustItem>
              <TrustNumber variant="h3" chinese>600</TrustNumber>
              <TrustLabel variant="caption" chinese>90天咨询量</TrustLabel>
            </TrustItem>
            <TrustItem>
              <TrustNumber variant="h3" chinese>50%+</TrustNumber>
              <TrustLabel variant="caption" chinese>续费率</TrustLabel>
            </TrustItem>
            <TrustItem>
              <TrustNumber variant="h3" chinese>80%</TrustNumber>
              <TrustLabel variant="caption" chinese>年轻客户</TrustLabel>
            </TrustItem>
          </TrustIndicators>
        </CTACard>
      </CTAContainer>
    </CTASection>
  );
};

export default CallToAction;