import React, { useState, useRef, useEffect } from 'react';
import type { ReactNode } from 'react';
import styled, { keyframes } from 'styled-components';

interface LazyContentProps {
  children: ReactNode;
  placeholder?: ReactNode;
  className?: string;
  rootMargin?: string;
  threshold?: number;
  fallback?: ReactNode;
}

const fadeIn = keyframes`
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

const ContentContainer = styled.div<{ $loaded: boolean }>`
  opacity: ${({ $loaded }) => ($loaded ? 1 : 0)};
  animation: ${({ $loaded }) => ($loaded ? fadeIn : 'none')} 
    ${({ theme }) => theme.animations.duration.normal} 
    ${({ theme }) => theme.animations.easing.easeOut};
`;

const PlaceholderContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: ${({ theme }) => theme.colors.gray[500]};
  font-size: ${({ theme }) => theme.typography.fontSizes.sm};
`;

const DefaultPlaceholder = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
`;

const LoadingSpinner = styled.div`
  width: 32px;
  height: 32px;
  border: 3px solid ${({ theme }) => theme.colors.gray[200]};
  border-top: 3px solid ${({ theme }) => theme.colors.trustBlue};
  border-radius: 50%;
  animation: spin 1s linear infinite;
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

export const LazyContent: React.FC<LazyContentProps> = ({
  children,
  placeholder,
  className,
  rootMargin = '100px',
  threshold = 0.1,
  fallback
}) => {
  const [inView, setInView] = useState(false);
  const [loaded, setLoaded] = useState(false);
  const [error] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setInView(true);
          observer.disconnect();
        }
      },
      {
        rootMargin,
        threshold
      }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => observer.disconnect();
  }, [rootMargin, threshold]);

  useEffect(() => {
    if (inView) {
      // 模拟内容加载延迟
      const timer = setTimeout(() => {
        setLoaded(true);
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [inView]);

  const defaultPlaceholder = (
    <DefaultPlaceholder>
      <LoadingSpinner />
      <span>内容加载中...</span>
    </DefaultPlaceholder>
  );

  if (error && fallback) {
    return <div className={className}>{fallback}</div>;
  }

  return (
    <div ref={containerRef} className={className}>
      {!inView || !loaded ? (
        <PlaceholderContainer>
          {placeholder || defaultPlaceholder}
        </PlaceholderContainer>
      ) : (
        <ContentContainer $loaded={loaded}>
          {children}
        </ContentContainer>
      )}
    </div>
  );
};

export default LazyContent;