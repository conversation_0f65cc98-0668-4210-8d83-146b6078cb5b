import { createGlobalStyle } from 'styled-components';

export const GlobalStyles = createGlobalStyle`
  /* CSS Reset and base styles */
  *, *::before, *::after {
    box-sizing: border-box;
  }

  * {
    margin: 0;
    padding: 0;
  }

  html {
    height: 100%;
    /* Responsive font scaling */
    font-size: 14px;
    
    ${({ theme }) => theme.mediaQueries.tablet} {
      font-size: 15px;
    }
    
    ${({ theme }) => theme.mediaQueries.desktop} {
      font-size: 16px;
    }
  }

  body {
    height: 100%;
    font-family: ${({ theme }) => theme.typography.fonts.chinese};
    font-size: ${({ theme }) => theme.typography.fontSizes.base};
    line-height: ${({ theme }) => theme.typography.lineHeights.normal};
    color: ${({ theme }) => theme.colors.gray[800]};
    background-color: ${({ theme }) => theme.colors.background};
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* Prevent horizontal scroll on mobile */
    overflow-x: hidden;
    /* Improve touch scrolling on iOS */
    -webkit-overflow-scrolling: touch;
  }

  /* Chinese text styling */
  .chinese-text,
  [lang="zh"],
  [lang="zh-CN"] {
    font-family: ${({ theme }) => theme.typography.fonts.chinese};
  }

  /* English text styling */
  .english-text,
  [lang="en"] {
    font-family: ${({ theme }) => theme.typography.fonts.english};
  }

  /* Focus styles for accessibility - WCAG compliant */
  :focus {
    outline: 2px solid ${({ theme }) => theme.colors.trustBlue};
    outline-offset: 2px;
    /* Ensure focus is always visible */
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.trustBlue}40;
  }

  /* Enhanced focus visibility for keyboard users */
  :focus-visible {
    outline: 2px solid ${({ theme }) => theme.colors.trustBlue};
    outline-offset: 2px;
    box-shadow: 0 0 0 4px ${({ theme }) => theme.colors.trustBlue}40;
    /* Add subtle animation for better visibility */
    transition: box-shadow 0.2s ease-out;
  }

  /* Hide focus outline for mouse users but keep box-shadow for consistency */
  :focus:not(:focus-visible) {
    outline: none;
    box-shadow: 0 0 0 1px ${({ theme }) => theme.colors.trustBlue}20;
  }

  /* Special focus styles for interactive elements */
  button:focus,
  a:focus,
  [role="button"]:focus,
  [role="menuitem"]:focus,
  [role="tab"]:focus,
  input:focus,
  textarea:focus,
  select:focus {
    outline: 2px solid ${({ theme }) => theme.colors.trustBlue};
    outline-offset: 2px;
    box-shadow: 0 0 0 4px ${({ theme }) => theme.colors.trustBlue}30;
  }

  /* Focus styles for audio controls */
  [role="slider"]:focus,
  [aria-label*="播放"]:focus,
  [aria-label*="音量"]:focus,
  [aria-label*="进度"]:focus {
    outline: 2px solid ${({ theme }) => theme.colors.actionOrange};
    outline-offset: 2px;
    box-shadow: 0 0 0 4px ${({ theme }) => theme.colors.actionOrange}30;
  }

  /* Enhanced focus styles for skip links */
  [href="#main-content"]:focus,
  [href="#teacher"]:focus,
  [href="#outline"]:focus {
    outline: 3px solid ${({ theme }) => theme.colors.actionOrange};
    outline-offset: 2px;
    box-shadow: 0 0 0 6px ${({ theme }) => theme.colors.actionOrange}40;
  }

  /* Focus styles for expandable/collapsible elements */
  [aria-expanded]:focus {
    outline: 2px solid ${({ theme }) => theme.colors.growthGreen};
    outline-offset: 2px;
    box-shadow: 0 0 0 4px ${({ theme }) => theme.colors.growthGreen}30;
  }

  /* Focus styles for currently playing audio elements */
  [aria-pressed="true"]:focus,
  [data-playing="true"]:focus {
    outline: 2px solid ${({ theme }) => theme.colors.actionOrange};
    outline-offset: 2px;
    box-shadow: 0 0 0 4px ${({ theme }) => theme.colors.actionOrange}40;
  }

  /* Remove default button styles */
  button {
    background: none;
    border: none;
    padding: 0;
    margin: 0;
    font: inherit;
    cursor: pointer;
  }

  /* Remove default link styles */
  a {
    color: inherit;
    text-decoration: none;
  }

  /* Remove default list styles */
  ul, ol {
    list-style: none;
  }

  /* Image defaults */
  img {
    max-width: 100%;
    height: auto;
    display: block;
  }

  /* Form element defaults */
  input, textarea, select {
    font: inherit;
    color: inherit;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Responsive media defaults */
  @media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
    
    html {
      scroll-behavior: auto;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    * {
      border-color: currentColor !important;
    }
    
    /* Ensure sufficient contrast in high contrast mode */
    button, [role="button"] {
      border: 2px solid currentColor !important;
      background-color: transparent !important;
    }
    
    /* Make focus indicators more prominent */
    :focus, :focus-visible {
      outline: 3px solid currentColor !important;
      outline-offset: 2px !important;
      box-shadow: 0 0 0 6px currentColor !important;
    }
    
    /* Ensure text has sufficient contrast */
    body {
      background-color: white !important;
      color: black !important;
    }
    
    /* Make interactive elements more visible */
    a, button, [role="button"] {
      text-decoration: underline !important;
      font-weight: bold !important;
    }
  }

  /* Support for users who prefer reduced transparency */
  @media (prefers-reduced-transparency: reduce) {
    * {
      backdrop-filter: none !important;
      background-color: ${({ theme }) => theme.colors.surface} !important;
    }
  }

  /* Mobile touch optimizations */
  @media (max-width: 767px) {
    /* Improve tap targets on mobile */
    button, a, [role="button"] {
      min-height: ${({ theme }) => theme.touchTargets.minimum};
      min-width: ${({ theme }) => theme.touchTargets.minimum};
    }
    
    /* Prevent zoom on input focus */
    input, textarea, select {
      font-size: 16px;
    }
    
    /* Optimize scrolling performance */
    * {
      -webkit-transform: translateZ(0);
      transform: translateZ(0);
    }
  }

  /* Tablet optimizations */
  @media (min-width: 768px) and (max-width: 1023px) {
    /* Comfortable touch targets for tablet */
    button, a, [role="button"] {
      min-height: ${({ theme }) => theme.touchTargets.comfortable};
      min-width: ${({ theme }) => theme.touchTargets.comfortable};
    }
  }

  /* Print styles */
  @media print {
    * {
      background: transparent !important;
      color: black !important;
      box-shadow: none !important;
      text-shadow: none !important;
    }
  }
`;

export default GlobalStyles;