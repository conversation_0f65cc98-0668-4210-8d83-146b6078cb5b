/* Font loading for WebUI brand typography */

/* 霞鹜文楷 (LXGW WenKai) - Chinese font */
@import url('https://fonts.googleapis.com/css2?family=LXGW+WenKai:wght@300;400;700&display=swap');

/* Nunito - English font */
@import url('https://fonts.googleapis.com/css2?family=Nunito:ital,wght@0,300;0,400;0,500;0,600;0,700;1,400;1,500&display=swap');

/* Font face declarations for better control */
@font-face {
  font-family: '霞鹜文楷';
  src: local('LXGW WenKai'), local('霞鹜文楷');
  font-display: swap;
}

/* Global font loading optimization */
:root {
  --font-chinese: "霞鹜文楷", "LXGW WenKai", serif;
  --font-english: "Nunito", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-system: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, sans-serif;
}

/* Preload critical fonts */
body {
  font-family: var(--font-english);
  font-display: swap;
}

/* Chinese text styling */
.chinese-text,
[lang="zh"],
[lang="zh-CN"] {
  font-family: var(--font-chinese);
}

/* English text styling */
.english-text,
[lang="en"] {
  font-family: var(--font-english);
}