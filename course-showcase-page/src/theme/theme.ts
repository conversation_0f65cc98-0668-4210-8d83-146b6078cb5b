import type { DefaultTheme } from 'styled-components';
import {
  colors,
  typography,
  spacing,
  breakpoints,
  borderRadius,
  shadows,
  animations,
  touchTargets
} from './tokens';

// Extend styled-components DefaultTheme
declare module 'styled-components' {
  export interface DefaultTheme {
    colors: typeof colors;
    typography: typeof typography;
    spacing: typeof spacing;
    breakpoints: typeof breakpoints;
    borderRadius: typeof borderRadius;
    shadows: typeof shadows;
    animations: typeof animations;
    touchTargets: typeof touchTargets;

    // Helper functions
    mediaQueries: {
      mobile: string;
      tablet: string;
      desktop: string;
      wide: string;
    };
  }
}

// Create the theme object
export const theme: DefaultTheme = {
  colors,
  typography,
  spacing,
  breakpoints,
  borderRadius,
  shadows,
  animations,
  touchTargets,

  // Media query helpers
  mediaQueries: {
    mobile: `@media (min-width: ${breakpoints.mobile})`,
    tablet: `@media (min-width: ${breakpoints.tablet})`,
    desktop: `@media (min-width: ${breakpoints.desktop})`,
    wide: `@media (min-width: ${breakpoints.wide})`,
  },
};

export default theme;