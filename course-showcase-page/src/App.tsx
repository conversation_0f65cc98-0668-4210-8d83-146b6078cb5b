import { ThemeProvider } from './theme'
import { CourseShowcasePage } from './components'
import { ErrorBoundary } from './components/ErrorBoundary'
import { SimpleAudioPlayerUI } from './components/SimpleAudioPlayerUI'
import { AudioProvider } from './hooks'
import { SimpleAudioProvider } from './hooks/useSimpleAudio'
import { ConfigProvider } from './contexts/ConfigContext'
import { AdminPage } from './pages/AdminPage'
import { performanceMonitor } from './utils/performanceMonitor'
import { initBrowserCompatibility } from './utils/browserCompatibility'
import { useEffect, useState } from 'react'

function App() {
  const [currentPage, setCurrentPage] = useState<'home' | 'admin'>('home');

  useEffect(() => {
    // 检查URL路径来决定显示哪个页面
    const path = window.location.pathname;
    if (path === '/admin') {
      setCurrentPage('admin');
    }
  }, []);

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>课程展示页面 🎓</h1>
      <p>当前页面: {currentPage}</p>
      <div style={{ marginBottom: '20px' }}>
        <button
          onClick={() => setCurrentPage('home')}
          style={{ marginRight: '10px', padding: '10px 20px' }}
        >
          主页
        </button>
        <button
          onClick={() => setCurrentPage('admin')}
          style={{ padding: '10px 20px' }}
        >
          管理页面
        </button>
      </div>

      {currentPage === 'admin' ? (
        <div style={{ padding: '20px', border: '1px solid #ccc', borderRadius: '8px' }}>
          <h2>管理页面</h2>
          <p>这里是课程管理界面</p>
          <p>功能包括：</p>
          <ul>
            <li>添加/编辑/删除课程模块</li>
            <li>拖拽排序</li>
            <li>文件上传</li>
            <li>配置管理</li>
          </ul>
        </div>
      ) : (
        <div style={{ padding: '20px', border: '1px solid #ccc', borderRadius: '8px' }}>
          <h2>课程展示页面</h2>
          <p>这里是主要的课程展示内容</p>
          <p>包含：</p>
          <ul>
            <li>课程介绍</li>
            <li>讲师信息</li>
            <li>课程大纲</li>
            <li>报名信息</li>
          </ul>
        </div>
      )}

      <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#f0f0f0', borderRadius: '4px' }}>
        <p><strong>服务器状态:</strong></p>
        <p>前端: ✅ http://localhost:5173/</p>
        <p>后端: ✅ http://localhost:3001/</p>
      </div>
    </div>
  )
}

export default App
