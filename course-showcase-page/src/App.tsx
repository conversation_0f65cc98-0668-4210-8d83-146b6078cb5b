import { ThemeProvider } from './theme'
import { CourseShowcasePage } from './components'
import { ErrorBoundary } from './components/ErrorBoundary'
import { SimpleAudioPlayerUI } from './components/SimpleAudioPlayerUI'
import { AudioProvider } from './hooks'
import { SimpleAudioProvider } from './hooks/useSimpleAudio'
import { ConfigProvider } from './contexts/ConfigContext'
import { AdminPage } from './pages/AdminPage'
import { performanceMonitor } from './utils/performanceMonitor'
import { initBrowserCompatibility } from './utils/browserCompatibility'
import { useEffect, useState } from 'react'

function App() {
  const [currentPage, setCurrentPage] = useState<'home' | 'admin'>('home');

  useEffect(() => {
    // 简化的应用初始化 - 只在生产环境启用监控
    if (process.env.NODE_ENV === 'production') {
      performanceMonitor.markStart('app-init');
      initBrowserCompatibility();
    }

    // 检查URL路径来决定显示哪个页面
    const path = window.location.pathname;
    if (path === '/admin') {
      setCurrentPage('admin');
    }

    // 监听浏览器前进后退
    const handlePopState = () => {
      const path = window.location.pathname;
      setCurrentPage(path === '/admin' ? 'admin' : 'home');
    };

    window.addEventListener('popstate', handlePopState);

    // 清理函数
    return () => {
      if (process.env.NODE_ENV === 'production') {
        performanceMonitor.markEnd('app-init');
      }
      window.removeEventListener('popstate', handlePopState);
    }
  }, []);

  return (
    <ErrorBoundary>
      <ConfigProvider>
        <ThemeProvider>
          <AudioProvider>
            <SimpleAudioProvider>
              <ErrorBoundary>
                {currentPage === 'admin' ? (
                  <AdminPage />
                ) : (
                  <>
                    <CourseShowcasePage />
                    {/* 使用简化版音频播放器 */}
                    <SimpleAudioPlayerUI />
                    {/* 音频播放功能已正常工作，移除调试工具 */}
                  </>
                )}
              </ErrorBoundary>
            </SimpleAudioProvider>
          </AudioProvider>
        </ThemeProvider>
      </ConfigProvider>
    </ErrorBoundary>
  )
}

export default App
