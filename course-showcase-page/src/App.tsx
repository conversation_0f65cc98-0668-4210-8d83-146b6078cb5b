import { ThemeProvider } from './theme'
import { CourseShowcasePage } from './components'
import { ErrorBoundary } from './components/ErrorBoundary'
import { SimpleAudioPlayerUI } from './components/SimpleAudioPlayerUI'
import { AudioProvider } from './hooks'
import { SimpleAudioProvider } from './hooks/useSimpleAudio'
import { ConfigProvider } from './contexts/ConfigContext'
import { performanceMonitor } from './utils/performanceMonitor'
import { initBrowserCompatibility } from './utils/browserCompatibility'
import { useEffect } from 'react'

function App() {
  // 简化的应用初始化 - 只在生产环境启用监控
  useEffect(() => {
    if (process.env.NODE_ENV === 'production') {
      performanceMonitor.markStart('app-init');
      initBrowserCompatibility();
      
      return () => {
        performanceMonitor.markEnd('app-init');
      };
    }
  }, []);

  return (
    <ErrorBoundary>
      <ConfigProvider>
        <ThemeProvider>
          <AudioProvider>
            <SimpleAudioProvider>
              <ErrorBoundary>
                <CourseShowcasePage />
              </ErrorBoundary>
              {/* 使用简化版音频播放器 */}
              <SimpleAudioPlayerUI />
              {/* 音频播放功能已正常工作，移除调试工具 */}
            </SimpleAudioProvider>
          </AudioProvider>
        </ThemeProvider>
      </ConfigProvider>
    </ErrorBoundary>
  )
}

export default App
