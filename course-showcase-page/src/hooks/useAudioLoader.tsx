import { useState, useCallback, useRef, useEffect } from 'react';
import type { Lesson } from '../types';

interface AudioLoadState {
  isLoading: boolean;
  error: string | null;
  retryCount: number;
  progress: number;
}

interface UseAudioLoaderOptions {
  maxRetries?: number;
  retryDelay?: number;
  preloadNext?: boolean;
  onLoadStart?: () => void;
  onLoadEnd?: () => void;
  onError?: (error: string) => void;
}

interface UseAudioLoaderReturn {
  loadState: AudioLoadState;
  loadAudio: (lesson: Lesson) => Promise<void>;
  retryLoad: () => Promise<void>;
  preloadAudio: (lesson: Lesson) => Promise<void>;
  clearCache: () => void;
}

// 音频缓存管理
class AudioCache {
  private cache = new Map<string, HTMLAudioElement>();
  private preloadQueue = new Set<string>();
  private maxCacheSize = 10; // 最多缓存10个音频文件

  get(url: string): HTMLAudioElement | undefined {
    return this.cache.get(url);
  }

  set(url: string, audio: HTMLAudioElement): void {
    // 如果缓存已满，删除最旧的条目
    if (this.cache.size >= this.maxCacheSize) {
      const firstKey = this.cache.keys().next().value;
      if (firstKey) {
        const oldAudio = this.cache.get(firstKey);
        if (oldAudio) {
          oldAudio.src = '';
          oldAudio.load();
        }
        this.cache.delete(firstKey);
      }
    }
    this.cache.set(url, audio);
  }

  has(url: string): boolean {
    return this.cache.has(url);
  }

  delete(url: string): void {
    const audio = this.cache.get(url);
    if (audio) {
      audio.src = '';
      audio.load();
    }
    this.cache.delete(url);
    this.preloadQueue.delete(url);
  }

  clear(): void {
    this.cache.forEach((audio) => {
      audio.src = '';
      audio.load();
    });
    this.cache.clear();
    this.preloadQueue.clear();
  }

  isPreloading(url: string): boolean {
    return this.preloadQueue.has(url);
  }

  setPreloading(url: string, isPreloading: boolean): void {
    if (isPreloading) {
      this.preloadQueue.add(url);
    } else {
      this.preloadQueue.delete(url);
    }
  }
}

const audioCache = new AudioCache();

export const useAudioLoader = (options: UseAudioLoaderOptions = {}): UseAudioLoaderReturn => {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    preloadNext: _preloadNext = true,
    onLoadStart,
    onLoadEnd,
    onError
  } = options;

  const [loadState, setLoadState] = useState<AudioLoadState>({
    isLoading: false,
    error: null,
    retryCount: 0,
    progress: 0
  });

  const currentLessonRef = useRef<Lesson | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // 清理函数
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  const createAudioElement = useCallback((url: string): Promise<HTMLAudioElement> => {
    return new Promise((resolve, reject) => {
      const audio = new HTMLAudioElement();
      const abortController = new AbortController();
      abortControllerRef.current = abortController;

      // 设置音频属性
      audio.preload = 'metadata';
      audio.crossOrigin = 'anonymous';
      
      // 进度监听
      const handleProgress = () => {
        if (audio.buffered.length > 0) {
          const progress = (audio.buffered.end(0) / audio.duration) * 100;
          setLoadState(prev => ({ ...prev, progress }));
        }
      };

      // 成功加载
      const handleCanPlayThrough = () => {
        cleanup();
        setLoadState(prev => ({ 
          ...prev, 
          isLoading: false, 
          error: null, 
          progress: 100 
        }));
        resolve(audio);
      };

      // 加载错误
      const handleError = () => {
        cleanup();
        const errorMessage = `音频加载失败: ${audio.error?.message || '未知错误'}`;
        setLoadState(prev => ({ 
          ...prev, 
          isLoading: false, 
          error: errorMessage 
        }));
        reject(new Error(errorMessage));
      };

      // 加载开始
      const handleLoadStart = () => {
        setLoadState(prev => ({ 
          ...prev, 
          isLoading: true, 
          error: null, 
          progress: 0 
        }));
      };

      // 中止加载
      const handleAbort = () => {
        cleanup();
        reject(new Error('音频加载被中止'));
      };

      const cleanup = () => {
        audio.removeEventListener('canplaythrough', handleCanPlayThrough);
        audio.removeEventListener('error', handleError);
        audio.removeEventListener('loadstart', handleLoadStart);
        audio.removeEventListener('progress', handleProgress);
        audio.removeEventListener('abort', handleAbort);
      };

      // 监听中止信号
      abortController.signal.addEventListener('abort', handleAbort);

      // 添加事件监听器
      audio.addEventListener('canplaythrough', handleCanPlayThrough);
      audio.addEventListener('error', handleError);
      audio.addEventListener('loadstart', handleLoadStart);
      audio.addEventListener('progress', handleProgress);
      audio.addEventListener('abort', handleAbort);

      // 开始加载
      audio.src = url;
      audio.load();
    });
  }, []);

  const loadAudio = useCallback(async (lesson: Lesson): Promise<void> => {
    currentLessonRef.current = lesson;
    
    // 中止之前的加载
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // 检查缓存
    if (audioCache.has(lesson.audioUrl)) {
      // const cachedAudio = audioCache.get(lesson.audioUrl)!;
      setLoadState({
        isLoading: false,
        error: null,
        retryCount: 0,
        progress: 100
      });
      onLoadEnd?.();
      return;
    }

    onLoadStart?.();

    let retryCount = 0;
    
    const attemptLoad = async (): Promise<void> => {
      try {
        setLoadState(prev => ({ 
          ...prev, 
          retryCount, 
          isLoading: true, 
          error: null 
        }));

        const audio = await createAudioElement(lesson.audioUrl);
        audioCache.set(lesson.audioUrl, audio);
        
        onLoadEnd?.();
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '音频加载失败';
        
        if (retryCount < maxRetries) {
          retryCount++;
          console.warn(`音频加载失败，正在重试 (${retryCount}/${maxRetries}):`, errorMessage);
          
          // 延迟重试
          await new Promise(resolve => setTimeout(resolve, retryDelay * retryCount));
          
          // 检查是否仍在加载同一课程
          if (currentLessonRef.current?.id === lesson.id) {
            return attemptLoad();
          }
        } else {
          setLoadState(prev => ({ 
            ...prev, 
            isLoading: false, 
            error: `加载失败，已重试${maxRetries}次: ${errorMessage}`,
            retryCount 
          }));
          onError?.(errorMessage);
          throw error;
        }
      }
    };

    return attemptLoad();
  }, [createAudioElement, maxRetries, retryDelay, onLoadStart, onLoadEnd, onError]);

  const retryLoad = useCallback(async (): Promise<void> => {
    if (currentLessonRef.current) {
      // 清除缓存中的失败项
      audioCache.delete(currentLessonRef.current.audioUrl);
      return loadAudio(currentLessonRef.current);
    }
  }, [loadAudio]);

  const preloadAudio = useCallback(async (lesson: Lesson): Promise<void> => {
    // 如果已经在缓存中或正在预加载，跳过
    if (audioCache.has(lesson.audioUrl) || audioCache.isPreloading(lesson.audioUrl)) {
      return;
    }

    audioCache.setPreloading(lesson.audioUrl, true);

    try {
      const audio = await createAudioElement(lesson.audioUrl);
      audioCache.set(lesson.audioUrl, audio);
    } catch (error) {
      console.warn('预加载音频失败:', lesson.title, error);
    } finally {
      audioCache.setPreloading(lesson.audioUrl, false);
    }
  }, [createAudioElement]);

  const clearCache = useCallback(() => {
    audioCache.clear();
    setLoadState({
      isLoading: false,
      error: null,
      retryCount: 0,
      progress: 0
    });
  }, []);

  return {
    loadState,
    loadAudio,
    retryLoad,
    preloadAudio,
    clearCache
  };
};

export default useAudioLoader;