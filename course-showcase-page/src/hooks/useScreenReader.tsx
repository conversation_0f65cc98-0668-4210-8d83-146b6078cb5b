import { useEffect, useRef } from 'react';

interface ScreenReaderAnnouncementOptions {
  priority?: 'polite' | 'assertive';
  delay?: number;
}

export const useScreenReaderAnnouncement = () => {
  const announcementRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    // Create a live region for screen reader announcements
    const liveRegion = document.createElement('div');
    liveRegion.setAttribute('aria-live', 'polite');
    liveRegion.setAttribute('aria-atomic', 'true');
    liveRegion.setAttribute('aria-relevant', 'text');
    liveRegion.style.position = 'absolute';
    liveRegion.style.left = '-10000px';
    liveRegion.style.width = '1px';
    liveRegion.style.height = '1px';
    liveRegion.style.overflow = 'hidden';
    
    document.body.appendChild(liveRegion);
    announcementRef.current = liveRegion;

    return () => {
      if (announcementRef.current && document.body.contains(announcementRef.current)) {
        document.body.removeChild(announcementRef.current);
      }
    };
  }, []);

  const announce = (
    message: string, 
    options: ScreenReaderAnnouncementOptions = {}
  ) => {
    const { priority = 'polite', delay = 100 } = options;
    
    if (!announcementRef.current) return;

    // Update the aria-live priority
    announcementRef.current.setAttribute('aria-live', priority);
    
    // Clear previous message
    announcementRef.current.textContent = '';
    
    // Add new message with a slight delay to ensure screen readers pick it up
    setTimeout(() => {
      if (announcementRef.current) {
        announcementRef.current.textContent = message;
      }
    }, delay);
  };

  return { announce };
};

export default useScreenReaderAnnouncement;