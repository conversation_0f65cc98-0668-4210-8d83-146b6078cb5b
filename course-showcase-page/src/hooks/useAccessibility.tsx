import { useEffect, useRef, useCallback } from 'react';
import { useScreenReaderAnnouncement } from './useScreenReader';

interface AccessibilityOptions {
  announceStateChanges?: boolean;
  enableKeyboardShortcuts?: boolean;
  focusManagement?: boolean;
}

interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  shiftKey?: boolean;
  altKey?: boolean;
  action: () => void;
  description: string;
}

export const useAccessibility = (options: AccessibilityOptions = {}) => {
  const {
    announceStateChanges = true,
    enableKeyboardShortcuts = true,
    focusManagement = true,
  } = options;

  const { announce } = useScreenReaderAnnouncement();
  const keyboardShortcuts = useRef<KeyboardShortcut[]>([]);
  const focusHistory = useRef<HTMLElement[]>([]);

  // Announce state changes for screen readers
  const announceChange = useCallback((
    message: string,
    priority: 'polite' | 'assertive' = 'polite'
  ) => {
    if (announceStateChanges) {
      announce(message, { priority });
    }
  }, [announce, announceStateChanges]);

  // Register keyboard shortcuts
  const registerShortcut = useCallback((shortcut: KeyboardShortcut) => {
    keyboardShortcuts.current.push(shortcut);
  }, []);

  // Unregister keyboard shortcuts
  const unregisterShortcut = useCallback((key: string) => {
    keyboardShortcuts.current = keyboardShortcuts.current.filter(
      shortcut => shortcut.key !== key
    );
  }, []);

  // Focus management utilities
  const saveFocus = useCallback(() => {
    if (focusManagement && document.activeElement instanceof HTMLElement) {
      focusHistory.current.push(document.activeElement);
    }
  }, [focusManagement]);

  const restoreFocus = useCallback(() => {
    if (focusManagement && focusHistory.current.length > 0) {
      const lastFocused = focusHistory.current.pop();
      if (lastFocused && document.contains(lastFocused)) {
        lastFocused.focus();
      }
    }
  }, [focusManagement]);

  const focusElement = useCallback((selector: string | HTMLElement) => {
    if (!focusManagement) return;

    const element = typeof selector === 'string' 
      ? document.querySelector(selector) as HTMLElement
      : selector;

    if (element && element.focus) {
      saveFocus();
      element.focus();
      
      // Announce focus change for screen readers
      const label = element.getAttribute('aria-label') || 
                   element.getAttribute('title') || 
                   element.textContent?.trim() || 
                   '元素';
      announceChange(`焦点移动到: ${label}`, 'polite');
    }
  }, [focusManagement, saveFocus, announceChange]);

  // Trap focus within a container (for modals, menus, etc.)
  const trapFocus = useCallback((container: HTMLElement) => {
    if (!focusManagement) return () => {};

    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    ) as NodeListOf<HTMLElement>;

    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          e.preventDefault();
          lastElement.focus();
        }
      } else {
        if (document.activeElement === lastElement) {
          e.preventDefault();
          firstElement.focus();
        }
      }
    };

    container.addEventListener('keydown', handleTabKey);
    
    // Focus first element
    if (firstElement) {
      firstElement.focus();
    }

    return () => {
      container.removeEventListener('keydown', handleTabKey);
    };
  }, [focusManagement]);

  // Handle keyboard shortcuts
  useEffect(() => {
    if (!enableKeyboardShortcuts) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      // Don't handle shortcuts when typing in input fields
      if (event.target instanceof HTMLInputElement || 
          event.target instanceof HTMLTextAreaElement) {
        return;
      }

      const matchingShortcut = keyboardShortcuts.current.find(shortcut => 
        shortcut.key === event.key &&
        !!shortcut.ctrlKey === event.ctrlKey &&
        !!shortcut.shiftKey === event.shiftKey &&
        !!shortcut.altKey === event.altKey
      );

      if (matchingShortcut) {
        event.preventDefault();
        matchingShortcut.action();
        announceChange(`执行快捷键: ${matchingShortcut.description}`, 'polite');
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [enableKeyboardShortcuts, announceChange]);

  // Announce available keyboard shortcuts
  const announceShortcuts = useCallback(() => {
    if (keyboardShortcuts.current.length === 0) {
      announceChange('当前没有可用的键盘快捷键', 'polite');
      return;
    }

    const shortcutList = keyboardShortcuts.current
      .map(shortcut => {
        let keys = [];
        if (shortcut.ctrlKey) keys.push('Ctrl');
        if (shortcut.shiftKey) keys.push('Shift');
        if (shortcut.altKey) keys.push('Alt');
        keys.push(shortcut.key);
        return `${keys.join('+')} - ${shortcut.description}`;
      })
      .join(', ');

    announceChange(`可用快捷键: ${shortcutList}`, 'polite');
  }, [announceChange]);

  // Validate color contrast
  const validateContrast = useCallback((
    _foreground: string,
    _background: string,
    element?: HTMLElement
  ) => {
    // This would integrate with the colorContrast utility
    // For now, we'll just announce if contrast might be insufficient
    if (element) {
      const computedStyle = window.getComputedStyle(element);
      const fg = computedStyle.color;
      const bg = computedStyle.backgroundColor;
      
      // Simple heuristic - in a real implementation, we'd use the colorContrast utility
      if (fg === bg) {
        announceChange('警告: 文本颜色对比度可能不足', 'assertive');
      }
    }
  }, [announceChange]);

  // Check if user prefers reduced motion
  const prefersReducedMotion = useCallback(() => {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  }, []);

  // Check if user prefers high contrast
  const prefersHighContrast = useCallback(() => {
    return window.matchMedia('(prefers-contrast: high)').matches;
  }, []);

  return {
    // Announcement functions
    announceChange,
    
    // Keyboard shortcut management
    registerShortcut,
    unregisterShortcut,
    announceShortcuts,
    
    // Focus management
    saveFocus,
    restoreFocus,
    focusElement,
    trapFocus,
    
    // Accessibility validation
    validateContrast,
    
    // User preference detection
    prefersReducedMotion,
    prefersHighContrast,
  };
};

export default useAccessibility;