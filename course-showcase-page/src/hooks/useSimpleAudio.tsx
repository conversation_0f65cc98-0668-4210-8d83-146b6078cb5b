import { useState, useRef, useEffect, createContext, useContext } from 'react';
import type { ReactNode } from 'react';
import type { Lesson } from '../types';

// 简化的音频状态
interface SimpleAudioState {
  currentLesson: Lesson | null;
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  playbackRate: number;
  error: string | null;
}

// 简化的音频上下文
interface SimpleAudioContextType {
  state: SimpleAudioState;
  playLesson: (lesson: Lesson) => void;
  pauseAudio: () => void;
  resumeAudio: () => void;
  setVolume: (volume: number) => void;
  setPlaybackRate: (rate: number) => void;
  seekTo: (time: number) => void;
  setOnAudioEnded: (callback: (() => void) | null) => void;
}

const SimpleAudioContext = createContext<SimpleAudioContextType | undefined>(undefined);

// 初始状态
const initialState: SimpleAudioState = {
  currentLesson: null,
  isPlaying: false,
  currentTime: 0,
  duration: 0,
  volume: 0.8,
  playbackRate: 1,
  error: null
};

export const SimpleAudioProvider = ({ children }: { children: ReactNode }) => {
  const [state, setState] = useState<SimpleAudioState>(initialState);
  const audioRef = useRef<HTMLAudioElement>(null);
  const onAudioEndedRef = useRef<(() => void) | null>(null);

  // 创建音频元素
  useEffect(() => {
    const audio = new Audio();
    audio.preload = 'metadata';
    audio.volume = 0.8; // 使用固定的初始音量
    audioRef.current = audio;

    // 音频事件监听器
    const handleLoadedMetadata = () => {
      console.log('Audio metadata loaded, duration:', audio.duration);
      setState(prev => ({ ...prev, duration: audio.duration, error: null }));
    };

    const handleTimeUpdate = () => {
      setState(prev => ({ ...prev, currentTime: audio.currentTime }));
    };

    const handlePlay = () => {
      console.log('Audio started playing');
      setState(prev => ({ ...prev, isPlaying: true, error: null }));
    };

    const handlePause = () => {
      console.log('Audio paused');
      setState(prev => ({ ...prev, isPlaying: false }));
    };

    const handleError = () => {
      console.error('Audio error:', audio.error);
      const errorMessage = audio.error ?
        `播放错误: ${audio.error.message}` :
        '音频播放失败';
      setState(prev => ({
        ...prev,
        isPlaying: false,
        error: errorMessage
      }));
    };

    const handleEnded = () => {
      console.log('Audio ended');
      setState(prev => ({ ...prev, isPlaying: false, currentTime: 0 }));

      // 如果有自动播放回调，执行它
      if (onAudioEndedRef.current) {
        console.log('Executing auto-play callback');
        onAudioEndedRef.current();
      }
    };

    // 添加事件监听器
    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('play', handlePlay);
    audio.addEventListener('pause', handlePause);
    audio.addEventListener('error', handleError);
    audio.addEventListener('ended', handleEnded);

    // 清理函数
    return () => {
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('play', handlePlay);
      audio.removeEventListener('pause', handlePause);
      audio.removeEventListener('error', handleError);
      audio.removeEventListener('ended', handleEnded);
      audio.pause();
      audio.src = '';
    };
  }, []);

  // 单独处理音量变化
  useEffect(() => {
    const audio = audioRef.current;
    if (audio) {
      audio.volume = state.volume;
    }
  }, [state.volume]);

  // 单独处理播放速度变化
  useEffect(() => {
    const audio = audioRef.current;
    if (audio) {
      console.log('Hook: Setting playback rate to:', state.playbackRate);
      try {
        audio.playbackRate = state.playbackRate;
        console.log('Hook: Playback rate successfully set to:', audio.playbackRate);
      } catch (error) {
        console.error('Hook: Error setting playback rate:', error);
      }
    }
  }, [state.playbackRate]);

  // 播放课程
  const playLesson = (lesson: Lesson) => {
    console.log('playLesson called:', lesson.title, lesson.audioUrl);
    const audio = audioRef.current;
    if (!audio) return;

    try {
      // 设置新的音频源
      if (audio.src !== lesson.audioUrl) {
        console.log('Loading new audio:', lesson.audioUrl);
        audio.src = lesson.audioUrl;
        audio.load();
      }

      // 更新状态
      setState(prev => ({
        ...prev,
        currentLesson: lesson,
        error: null
      }));

      // 播放音频
      audio.play().then(() => {
        console.log('Audio play started successfully');
      }).catch((error) => {
        console.error('Play failed:', error);
        setState(prev => ({
          ...prev,
          error: `播放失败: ${error.message}`,
          isPlaying: false
        }));
      });

    } catch (error) {
      console.error('playLesson error:', error);
      setState(prev => ({
        ...prev,
        error: `播放失败: ${error instanceof Error ? error.message : '未知错误'}`,
        isPlaying: false
      }));
    }
  };

  // 暂停音频
  const pauseAudio = () => {
    console.log('pauseAudio called');
    const audio = audioRef.current;
    if (audio) {
      audio.pause();
    }
  };

  // 恢复播放
  const resumeAudio = () => {
    console.log('resumeAudio called');
    const audio = audioRef.current;
    if (audio && state.currentLesson) {
      audio.play().catch((error) => {
        console.error('Resume failed:', error);
        setState(prev => ({
          ...prev,
          error: `恢复播放失败: ${error.message}`,
          isPlaying: false
        }));
      });
    }
  };

  // 设置音量
  const setVolume = (volume: number) => {
    const audio = audioRef.current;
    if (audio) {
      audio.volume = Math.max(0, Math.min(1, volume));
      setState(prev => ({ ...prev, volume }));
    }
  };

  // 设置播放速度
  const setPlaybackRate = (rate: number) => {
    console.log('Hook: setPlaybackRate called with:', rate);
    const audio = audioRef.current;
    if (audio) {
      const clampedRate = Math.max(0.25, Math.min(4, rate)); // 限制在合理范围内
      console.log('Hook: Setting playback rate to:', clampedRate);
      try {
        audio.playbackRate = clampedRate;
        setState(prev => ({ ...prev, playbackRate: clampedRate }));
        console.log('Hook: Playback rate state updated to:', clampedRate);
      } catch (error) {
        console.error('Hook: Error in setPlaybackRate:', error);
      }
    } else {
      console.log('Hook: No audio element available for playback rate');
    }
  };

  // 跳转到指定时间
  const seekTo = (time: number) => {
    const audio = audioRef.current;
    if (audio) {
      audio.currentTime = time;
      setState(prev => ({ ...prev, currentTime: time }));
    }
  };

  // 设置音频结束回调
  const setOnAudioEnded = (callback: (() => void) | null) => {
    onAudioEndedRef.current = callback;
  };

  const contextValue: SimpleAudioContextType = {
    state,
    playLesson,
    pauseAudio,
    resumeAudio,
    setVolume,
    setPlaybackRate,
    seekTo,
    setOnAudioEnded
  };

  return (
    <SimpleAudioContext.Provider value={contextValue}>
      {children}
    </SimpleAudioContext.Provider>
  );
};

// 使用简化音频上下文的hook
export const useSimpleAudio = (): SimpleAudioContextType => {
  const context = useContext(SimpleAudioContext);
  if (context === undefined) {
    throw new Error('useSimpleAudio must be used within a SimpleAudioProvider');
  }
  return context;
};

export default useSimpleAudio;