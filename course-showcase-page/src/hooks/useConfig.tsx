import { useState, useEffect, useCallback } from 'react';
import type { BasicConfig, BusinessConfig, CourseData } from '../types';

// Default configurations for fallback
const defaultBasicConfig: BasicConfig = {
  seo: {
    title: "教育人7天咨询起盘营 - 遇见之屿",
    description: "石丽莉老师20年教育经验，帮助教育工作者快速转型心理咨询师。经验迁移+AI提效+理论补强，7天开启咨询事业。",
    keywords: "心理咨询师培训,教育转型,心理咨询,AI咨询,石丽莉",
    ogTitle: "教育人7天咨询起盘营 - 从教育经验到心理咨询的完美转型",
    ogDescription: "20年教育经验+AI赋能，帮您快速从教育工作者转型为专业心理咨询师",
    ogImage: "/images/og-course-cover.jpg"
  },
  brand: {
    name: "遇见之屿",
    logo: "/images/logo.png"
  },
  navigation: {
    teacher: { text: "老师介绍", url: "#teacher", openInNewWindow: false },
    positioning: { text: "课程定位", url: "#positioning", openInNewWindow: false },
    outline: { text: "课程大纲", url: "#outline", openInNewWindow: false },
    contact: { text: "联系我们", url: "#enrollment", openInNewWindow: false }
  },
  sections: {
    courseOutline: {
      title: "课程大纲",
      subtitle: "5大核心模块，从理论基础到实战应用，系统构建你的咨询能力"
    },
    coursePositioning: {
      title: "课程定位",
      subtitle: "精准匹配您的背景，量身定制成长路径",
      methodDescription: "三位一体的学习方法，让您快速从新手成长为专业咨询师"
    },
    teacherIntroduction: {
      titlePrefix: "老师介绍："
    }
  },
  buttons: {
    enrollNow: "立即报名",
    freeConsult: "免费咨询",
    processing: "正在处理...",
    playAudio: "播放",
    pauseAudio: "暂停"
  }
};

const defaultBusinessConfig: BusinessConfig = {
  cta: {
    title: "开启您的心理咨询师转型之旅",
    description: "加入\"教育人 7天咨询起盘营\"，让石老师的20年教育经验和AI赋能方法，帮助您快速从教育工作者转型为专业心理咨询师。",
    primaryButton: { text: "立即报名", url: "https://example.com/enroll", openInNewWindow: true },
    secondaryButton: { text: "免费咨询", url: "https://wa.me/your-whatsapp-number", openInNewWindow: true }
  },
  pricing: {
    currentPrice: "¥1,999",
    originalPrice: "¥2,999",
    priceLabel: "早鸟价",
    limitedOffer: "限时优惠，仅限前100名学员"
  },
  contact: {
    phone: "************",
    whatsapp: "https://wa.me/your-whatsapp-number",
    wechat: "your-wechat-id",
    wechatQrCode: "/images/qr-codes/wechat-qr.svg",
    email: "<EMAIL>",
    consultationQrCode: "/images/qr-codes/consultation-qr.svg"
  }
};

interface UseConfigReturn {
  basicConfig: BasicConfig;
  businessConfig: BusinessConfig;
  courseConfig: CourseData | null;
  isLoading: boolean;
  error: string | null;
  refreshConfig: () => Promise<void>;
}

export const useConfig = (): UseConfigReturn => {
  const [basicConfig, setBasicConfig] = useState<BasicConfig>(defaultBasicConfig);
  const [businessConfig, setBusinessConfig] = useState<BusinessConfig>(defaultBusinessConfig);
  const [courseConfig, setCourseConfig] = useState<CourseData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadConfig = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Load all three configuration files
      const [basicResponse, businessResponse, courseResponse] = await Promise.allSettled([
        fetch('/config/basic-config.json'),
        fetch('/config/business-config.json'),
        fetch('/config/course-config.json')
      ]);

      // Handle basic config
      if (basicResponse.status === 'fulfilled' && basicResponse.value.ok) {
        const basicData = await basicResponse.value.json();
        setBasicConfig({ ...defaultBasicConfig, ...basicData });
      } else {
        console.warn('Failed to load basic config, using defaults');
      }

      // Handle business config
      if (businessResponse.status === 'fulfilled' && businessResponse.value.ok) {
        const businessData = await businessResponse.value.json();
        setBusinessConfig({ ...defaultBusinessConfig, ...businessData });
      } else {
        console.warn('Failed to load business config, using defaults');
      }

      // Handle course config
      if (courseResponse.status === 'fulfilled' && courseResponse.value.ok) {
        const courseData = await courseResponse.value.json();
        setCourseConfig(courseData);
      } else {
        // Fallback to existing data structure
        const { courseData } = await import('../data');
        setCourseConfig(courseData);
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error loading configuration:', err);
      
      // Use fallback data
      const { courseData } = await import('../data');
      setCourseConfig(courseData);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshConfig = useCallback(async () => {
    await loadConfig();
  }, [loadConfig]);

  useEffect(() => {
    loadConfig();
  }, [loadConfig]);

  return {
    basicConfig,
    businessConfig,
    courseConfig,
    isLoading,
    error,
    refreshConfig
  };
};
