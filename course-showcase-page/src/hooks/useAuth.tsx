import { useState, useEffect, useCallback } from 'react';

interface AuthState {
  isAuthenticated: boolean;
  token: string | null;
  loading: boolean;
}

interface LoginResponse {
  success: boolean;
  message?: string;
  token?: string;
  expiresIn?: string;
  error?: string;
}

interface VerifyResponse {
  success: boolean;
  valid?: boolean;
  user?: any;
  error?: string;
}

const API_BASE = 'http://localhost:3001/api';
const TOKEN_KEY = 'course_management_token';
const TOKEN_EXPIRY_KEY = 'course_management_token_expiry';

export const useAuth = () => {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    token: null,
    loading: true
  });

  // 保存token到localStorage
  const saveToken = useCallback((token: string, expiresIn: string) => {
    localStorage.setItem(TOKEN_KEY, token);
    
    // 计算过期时间
    const expiryTime = new Date();
    if (expiresIn.endsWith('h')) {
      const hours = parseInt(expiresIn.replace('h', ''));
      expiryTime.setHours(expiryTime.getHours() + hours);
    } else if (expiresIn.endsWith('d')) {
      const days = parseInt(expiresIn.replace('d', ''));
      expiryTime.setDate(expiryTime.getDate() + days);
    } else {
      // 默认24小时
      expiryTime.setHours(expiryTime.getHours() + 24);
    }
    
    localStorage.setItem(TOKEN_EXPIRY_KEY, expiryTime.toISOString());
  }, []);

  // 清除token
  const clearToken = useCallback(() => {
    localStorage.removeItem(TOKEN_KEY);
    localStorage.removeItem(TOKEN_EXPIRY_KEY);
  }, []);

  // 检查token是否过期
  const isTokenExpired = useCallback(() => {
    const expiryTime = localStorage.getItem(TOKEN_EXPIRY_KEY);
    if (!expiryTime) return true;
    
    return new Date() > new Date(expiryTime);
  }, []);

  // 验证token有效性
  const verifyToken = useCallback(async (token: string): Promise<boolean> => {
    try {
      const response = await fetch(`${API_BASE}/auth/verify`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      const data: VerifyResponse = await response.json();
      return data.success && data.valid === true;
    } catch (error) {
      console.error('Token验证失败:', error);
      return false;
    }
  }, []);

  // 登录
  const login = useCallback(async (password: string): Promise<LoginResponse> => {
    try {
      const response = await fetch(`${API_BASE}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ password }),
      });

      const data: LoginResponse = await response.json();

      if (data.success && data.token) {
        saveToken(data.token, data.expiresIn || '24h');
        setAuthState({
          isAuthenticated: true,
          token: data.token,
          loading: false
        });
      }

      return data;
    } catch (error) {
      console.error('登录失败:', error);
      return {
        success: false,
        error: '网络错误，请重试'
      };
    }
  }, [saveToken]);

  // 退出登录
  const logout = useCallback(() => {
    clearToken();
    setAuthState({
      isAuthenticated: false,
      token: null,
      loading: false
    });
  }, [clearToken]);

  // 刷新token
  const refreshToken = useCallback(async (): Promise<boolean> => {
    const currentToken = authState.token || localStorage.getItem(TOKEN_KEY);
    if (!currentToken) return false;

    try {
      const response = await fetch(`${API_BASE}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${currentToken}`
        }
      });

      const data = await response.json();

      if (data.success && data.token) {
        saveToken(data.token, data.expiresIn || '24h');
        setAuthState(prev => ({
          ...prev,
          token: data.token
        }));
        return true;
      }

      return false;
    } catch (error) {
      console.error('刷新token失败:', error);
      return false;
    }
  }, [authState.token, saveToken]);

  // 获取认证头
  const getAuthHeaders = useCallback(() => {
    const token = authState.token || localStorage.getItem(TOKEN_KEY);
    return token ? { 'Authorization': `Bearer ${token}` } : {};
  }, [authState.token]);

  // 带认证的fetch请求
  const authenticatedFetch = useCallback(async (url: string, options: RequestInit = {}) => {
    const token = authState.token || localStorage.getItem(TOKEN_KEY);
    
    const headers = {
      'Content-Type': 'application/json',
      ...getAuthHeaders(),
      ...options.headers
    };

    return fetch(url, {
      ...options,
      headers
    });
  }, [authState.token, getAuthHeaders]);

  // 初始化认证状态
  useEffect(() => {
    const initAuth = async () => {
      const savedToken = localStorage.getItem(TOKEN_KEY);
      
      if (!savedToken) {
        setAuthState({
          isAuthenticated: false,
          token: null,
          loading: false
        });
        return;
      }

      // 检查token是否过期
      if (isTokenExpired()) {
        clearToken();
        setAuthState({
          isAuthenticated: false,
          token: null,
          loading: false
        });
        return;
      }

      // 验证token有效性
      const isValid = await verifyToken(savedToken);
      
      if (isValid) {
        setAuthState({
          isAuthenticated: true,
          token: savedToken,
          loading: false
        });
      } else {
        clearToken();
        setAuthState({
          isAuthenticated: false,
          token: null,
          loading: false
        });
      }
    };

    initAuth();
  }, [verifyToken, isTokenExpired, clearToken]);

  // 自动刷新token（在过期前1小时刷新）
  useEffect(() => {
    if (!authState.isAuthenticated || !authState.token) return;

    const checkAndRefreshToken = () => {
      const expiryTime = localStorage.getItem(TOKEN_EXPIRY_KEY);
      if (!expiryTime) return;

      const expiry = new Date(expiryTime);
      const now = new Date();
      const oneHourBeforeExpiry = new Date(expiry.getTime() - 60 * 60 * 1000);

      if (now > oneHourBeforeExpiry && now < expiry) {
        refreshToken();
      }
    };

    // 每10分钟检查一次
    const interval = setInterval(checkAndRefreshToken, 10 * 60 * 1000);
    
    return () => clearInterval(interval);
  }, [authState.isAuthenticated, authState.token, refreshToken]);

  return {
    ...authState,
    login,
    logout,
    refreshToken,
    getAuthHeaders,
    authenticatedFetch,
    isTokenExpired: isTokenExpired()
  };
};
