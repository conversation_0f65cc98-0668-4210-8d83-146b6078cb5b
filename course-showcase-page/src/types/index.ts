// Course Data Types
export interface Achievement {
  metric: string;
  value: string;
  description: string;
}

export interface TeacherProfile {
  name: string;
  age: number;
  experience: string;
  achievements: Achievement[];
  story: string;
  photoUrl: string;
}

export interface Lesson {
  id: string;
  title: string;
  duration: number; // in seconds
  audioUrl: string;
  description?: string;
  isCompleted: boolean;
}

export interface CourseModule {
  id: string;
  title: string;
  description: string;
  color: 'growth' | 'trust' | 'action'; // Maps to brand colors
  lessons: Lesson[];
}

export interface CoursePositioning {
  targetAudiences: {
    title: string;
    description: string;
  }[];
  coreSellingPoints: string[];
  uniqueValue: string;
}

export interface CourseData {
  title: string;
  subtitle: string;
  teacher: TeacherProfile;
  positioning: CoursePositioning;
  modules: CourseModule[];
}

// Audio Player Types
export interface AudioState {
  currentLesson: Lesson | null;
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  isLoading: boolean;
  playlist: Lesson[];
  currentIndex: number;
}