// Course Data Types
export interface Achievement {
  metric: string;
  value: string;
  description: string;
}

export interface TeacherProfile {
  name: string;
  age: number;
  experience: string;
  achievements: Achievement[];
  story: string;
  photoUrl: string;
}

export interface Lesson {
  id: string;
  title: string;
  duration: number; // in seconds
  audioUrl: string;
  description?: string;
  isCompleted: boolean;
  order: number; // For sorting lessons within a module
}

export interface CourseModule {
  id: string;
  title: string;
  description: string;
  color: 'growth' | 'trust' | 'action'; // Maps to brand colors
  lessons: Lesson[];
  order: number; // For sorting modules
}

export interface CoursePositioning {
  targetAudiences: {
    title: string;
    description: string;
  }[];
  coreSellingPoints: string[];
  uniqueValue: string;
}

export interface CourseData {
  title: string;
  subtitle: string;
  teacher: TeacherProfile;
  positioning: CoursePositioning;
  modules: CourseModule[];
}

// Audio Player Types
export interface AudioState {
  currentLesson: Lesson | null;
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  isLoading: boolean;
  playlist: Lesson[];
  currentIndex: number;
}

// Configuration Management Types
export interface NavigationItem {
  text: string;
  url: string;
  openInNewWindow: boolean;
}

export interface SEOConfig {
  title: string;
  description: string;
  keywords: string;
  ogTitle: string;
  ogDescription: string;
  ogImage: string;
}

export interface BasicConfig {
  seo: SEOConfig;
  brand: {
    name: string;
    logo: string;
  };
  navigation: {
    teacher: NavigationItem;
    positioning: NavigationItem;
    outline: NavigationItem;
    contact: NavigationItem;
  };
  sections: {
    courseOutline: {
      title: string;
      subtitle: string;
    };
    coursePositioning: {
      title: string;
      subtitle: string;
      methodDescription: string;
    };
    teacherIntroduction: {
      titlePrefix: string;
    };
  };
  buttons: {
    enrollNow: string;
    freeConsult: string;
    processing: string;
    playAudio: string;
    pauseAudio: string;
  };
}

export interface BusinessConfig {
  cta: {
    title: string;
    description: string;
    primaryButton: NavigationItem;
    secondaryButton: NavigationItem;
  };
  pricing: {
    currentPrice: string;
    originalPrice: string;
    priceLabel: string;
    limitedOffer: string;
  };
  contact: {
    phone: string;
    whatsapp: string;
    wechat: string;
    wechatQrCode: string;
    email: string;
    consultationQrCode: string;
  };
}

export interface ConfigContextType {
  basicConfig: BasicConfig | null;
  courseConfig: CourseData | null;
  businessConfig: BusinessConfig | null;
  isLoading: boolean;
  error: string | null;
  refreshConfig: () => Promise<void>;
}