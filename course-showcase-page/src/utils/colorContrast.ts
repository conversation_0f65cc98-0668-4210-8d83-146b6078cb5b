// Color contrast utilities for WCAG compliance

/**
 * Convert hex color to RGB values
 */
export const hexToRgb = (hex: string): { r: number; g: number; b: number } | null => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
};

/**
 * Calculate relative luminance of a color
 * Based on WCAG 2.1 guidelines
 */
export const getRelativeLuminance = (r: number, g: number, b: number): number => {
  const [rs, gs, bs] = [r, g, b].map(c => {
    c = c / 255;
    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
  });
  
  return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
};

/**
 * Calculate contrast ratio between two colors
 * Returns a value between 1 and 21
 */
export const getContrastRatio = (color1: string, color2: string): number => {
  const rgb1 = hexToRgb(color1);
  const rgb2 = hexToRgb(color2);
  
  if (!rgb1 || !rgb2) return 1;
  
  const lum1 = getRelativeLuminance(rgb1.r, rgb1.g, rgb1.b);
  const lum2 = getRelativeLuminance(rgb2.r, rgb2.g, rgb2.b);
  
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);
  
  return (brightest + 0.05) / (darkest + 0.05);
};

/**
 * Check if color combination meets WCAG AA standards
 * AA: 4.5:1 for normal text, 3:1 for large text
 */
export const meetsWCAGAA = (
  foreground: string, 
  background: string, 
  isLargeText: boolean = false
): boolean => {
  const ratio = getContrastRatio(foreground, background);
  return isLargeText ? ratio >= 3 : ratio >= 4.5;
};

/**
 * Check if color combination meets WCAG AAA standards
 * AAA: 7:1 for normal text, 4.5:1 for large text
 */
export const meetsWCAGAAA = (
  foreground: string, 
  background: string, 
  isLargeText: boolean = false
): boolean => {
  const ratio = getContrastRatio(foreground, background);
  return isLargeText ? ratio >= 4.5 : ratio >= 7;
};

/**
 * Get accessible text color (black or white) for a given background
 */
export const getAccessibleTextColor = (backgroundColor: string): string => {
  const whiteRatio = getContrastRatio('#FFFFFF', backgroundColor);
  const blackRatio = getContrastRatio('#000000', backgroundColor);
  
  return whiteRatio > blackRatio ? '#FFFFFF' : '#000000';
};

/**
 * Validate all brand colors for accessibility
 */
export const validateBrandColors = () => {
  const colors = {
    warmSand: '#F0EBE3',
    growthGreen: '#A3B899',
    trustBlue: '#6A8DAF',
    actionOrange: '#F7B787',
    white: '#FFFFFF',
    black: '#000000',
    gray800: '#1F2937',
    gray600: '#4B5563',
  };

  const combinations = [
    // Text on backgrounds
    { fg: colors.gray800, bg: colors.warmSand, name: 'Dark text on warm sand' },
    { fg: colors.white, bg: colors.trustBlue, name: 'White text on trust blue' },
    { fg: colors.white, bg: colors.growthGreen, name: 'White text on growth green' },
    { fg: colors.white, bg: colors.actionOrange, name: 'White text on action orange' },
    { fg: colors.trustBlue, bg: colors.white, name: 'Trust blue on white' },
    { fg: colors.growthGreen, bg: colors.white, name: 'Growth green on white' },
    { fg: colors.actionOrange, bg: colors.white, name: 'Action orange on white' },
    { fg: colors.gray600, bg: colors.white, name: 'Gray 600 on white' },
  ];

  const results = combinations.map(({ fg, bg, name }) => ({
    name,
    ratio: getContrastRatio(fg, bg),
    meetsAA: meetsWCAGAA(fg, bg),
    meetsAAA: meetsWCAGAAA(fg, bg),
  }));

  return results;
};

export default {
  hexToRgb,
  getRelativeLuminance,
  getContrastRatio,
  meetsWCAGAA,
  meetsWCAGAAA,
  getAccessibleTextColor,
  validateBrandColors,
};