// Audio utility functions for error handling and format support

export interface AudioError {
  code: number;
  message: string;
  type: 'network' | 'format' | 'decode' | 'unknown';
}

// Map HTML5 audio error codes to user-friendly messages
export const getAudioErrorMessage = (error: MediaError | null): AudioError => {
  if (!error) {
    return {
      code: 0,
      message: 'Unknown audio error',
      type: 'unknown'
    };
  }

  switch (error.code) {
    case MediaError.MEDIA_ERR_ABORTED:
      return {
        code: error.code,
        message: 'Audio playback was aborted',
        type: 'unknown'
      };
    case MediaError.MEDIA_ERR_NETWORK:
      return {
        code: error.code,
        message: 'Network error occurred while loading audio',
        type: 'network'
      };
    case MediaError.MEDIA_ERR_DECODE:
      return {
        code: error.code,
        message: 'Audio file could not be decoded',
        type: 'decode'
      };
    case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED:
      return {
        code: error.code,
        message: 'Audio format not supported',
        type: 'format'
      };
    default:
      return {
        code: error.code,
        message: 'Unknown audio error occurred',
        type: 'unknown'
      };
  }
};

// Check if browser supports audio format
export const isAudioFormatSupported = (mimeType: string): boolean => {
  const audio = document.createElement('audio');
  return audio.canPlayType(mimeType) !== '';
};

// Get supported audio formats in order of preference
export const getSupportedAudioFormats = (): string[] => {
  const formats = [
    'audio/mpeg', // MP3
    'audio/ogg',  // OGG
    'audio/wav',  // WAV
    'audio/mp4'   // MP4/AAC
  ];

  return formats.filter(isAudioFormatSupported);
};

// Format time in seconds to MM:SS format
export const formatTime = (seconds: number): string => {
  if (isNaN(seconds) || seconds < 0) return '0:00';
  
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

// Calculate progress percentage
export const calculateProgress = (currentTime: number, duration: number): number => {
  if (duration === 0 || isNaN(currentTime) || isNaN(duration)) return 0;
  return Math.min(100, (currentTime / duration) * 100);
};

// Retry mechanism for failed audio loads
export const retryAudioLoad = async (
  audioElement: HTMLAudioElement,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<void> => {
  let attempts = 0;
  
  const attemptLoad = (): Promise<void> => {
    return new Promise((resolve, reject) => {
      const handleCanPlay = () => {
        audioElement.removeEventListener('canplay', handleCanPlay);
        audioElement.removeEventListener('error', handleError);
        resolve();
      };
      
      const handleError = () => {
        audioElement.removeEventListener('canplay', handleCanPlay);
        audioElement.removeEventListener('error', handleError);
        reject(new Error('Audio load failed'));
      };
      
      audioElement.addEventListener('canplay', handleCanPlay);
      audioElement.addEventListener('error', handleError);
      audioElement.load();
    });
  };
  
  while (attempts < maxRetries) {
    try {
      await attemptLoad();
      return;
    } catch (error) {
      attempts++;
      if (attempts >= maxRetries) {
        throw new Error(`Failed to load audio after ${maxRetries} attempts`);
      }
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
};

// Preload audio for better user experience
export const preloadAudio = (url: string): Promise<HTMLAudioElement> => {
  return new Promise((resolve, reject) => {
    const audio = new Audio();
    
    const handleCanPlay = () => {
      audio.removeEventListener('canplay', handleCanPlay);
      audio.removeEventListener('error', handleError);
      resolve(audio);
    };
    
    const handleError = () => {
      audio.removeEventListener('canplay', handleCanPlay);
      audio.removeEventListener('error', handleError);
      reject(new Error('Failed to preload audio'));
    };
    
    audio.addEventListener('canplay', handleCanPlay);
    audio.addEventListener('error', handleError);
    audio.preload = 'metadata';
    audio.src = url;
  });
};