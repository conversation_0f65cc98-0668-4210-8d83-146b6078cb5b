// 浏览器兼容性检测和处理工具

interface BrowserInfo {
  name: string;
  version: string;
  isSupported: boolean;
  features: {
    webAudio: boolean;
    intersectionObserver: boolean;
    customProperties: boolean;
    flexbox: boolean;
    grid: boolean;
    webp: boolean;
  };
}

// 检测浏览器类型和版本
export const detectBrowser = (): BrowserInfo => {
  const userAgent = navigator.userAgent;
  let browserName = 'Unknown';
  let browserVersion = 'Unknown';

  // Chrome
  if (userAgent.indexOf('Chrome') > -1 && userAgent.indexOf('Edg') === -1) {
    browserName = 'Chrome';
    const match = userAgent.match(/Chrome\/(\d+)/);
    browserVersion = match ? match[1] : 'Unknown';
  }
  // Firefox
  else if (userAgent.indexOf('Firefox') > -1) {
    browserName = 'Firefox';
    const match = userAgent.match(/Firefox\/(\d+)/);
    browserVersion = match ? match[1] : 'Unknown';
  }
  // Safari
  else if (userAgent.indexOf('Safari') > -1 && userAgent.indexOf('Chrome') === -1) {
    browserName = 'Safari';
    const match = userAgent.match(/Version\/(\d+)/);
    browserVersion = match ? match[1] : 'Unknown';
  }
  // Edge
  else if (userAgent.indexOf('Edg') > -1) {
    browserName = 'Edge';
    const match = userAgent.match(/Edg\/(\d+)/);
    browserVersion = match ? match[1] : 'Unknown';
  }

  // 检测功能支持
  const features = {
    webAudio: !!(window.AudioContext || (window as any).webkitAudioContext),
    intersectionObserver: 'IntersectionObserver' in window,
    customProperties: CSS.supports('color', 'var(--test)'),
    flexbox: CSS.supports('display', 'flex'),
    grid: CSS.supports('display', 'grid'),
    webp: checkWebPSupport()
  };

  // 判断是否为支持的浏览器版本
  const isSupported = checkBrowserSupport(browserName, parseInt(browserVersion));

  return {
    name: browserName,
    version: browserVersion,
    isSupported,
    features
  };
};

// 检查WebP支持
const checkWebPSupport = (): boolean => {
  const canvas = document.createElement('canvas');
  canvas.width = 1;
  canvas.height = 1;
  return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
};

// 检查浏览器支持情况
const checkBrowserSupport = (name: string, version: number): boolean => {
  const minVersions: Record<string, number> = {
    Chrome: 90,
    Firefox: 88,
    Safari: 14,
    Edge: 90
  };

  return minVersions[name] ? version >= minVersions[name] : false;
};

// 添加浏览器特定的CSS类
export const addBrowserClasses = (): void => {
  const browserInfo = detectBrowser();
  const htmlElement = document.documentElement;

  // 添加浏览器类名
  htmlElement.classList.add(`browser-${browserInfo.name.toLowerCase()}`);
  htmlElement.classList.add(`browser-version-${browserInfo.version}`);

  // 添加功能支持类名
  Object.entries(browserInfo.features).forEach(([feature, supported]) => {
    htmlElement.classList.add(supported ? `supports-${feature}` : `no-${feature}`);
  });

  // 添加支持状态类名
  htmlElement.classList.add(browserInfo.isSupported ? 'browser-supported' : 'browser-unsupported');
};

// 显示浏览器不兼容警告
export const showBrowserWarning = (): void => {
  const browserInfo = detectBrowser();
  
  if (!browserInfo.isSupported) {
    const warningDiv = document.createElement('div');
    warningDiv.innerHTML = `
      <div style="
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        background: #f7b787;
        color: #333;
        padding: 12px;
        text-align: center;
        z-index: 10000;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      ">
        <strong>浏览器兼容性提醒：</strong>
        您当前使用的浏览器版本可能不支持所有功能。建议升级到最新版本以获得最佳体验。
        <button onclick="this.parentElement.parentElement.remove()" style="
          margin-left: 12px;
          background: none;
          border: 1px solid #333;
          padding: 4px 8px;
          cursor: pointer;
          border-radius: 4px;
        ">关闭</button>
      </div>
    `;
    document.body.appendChild(warningDiv);
  }
};

// 音频兼容性处理
export const createCompatibleAudioContext = (): AudioContext | null => {
  try {
    const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;
    return AudioContextClass ? new AudioContextClass() : null;
  } catch (error) {
    console.warn('AudioContext not supported:', error);
    return null;
  }
};

// Intersection Observer 兼容性处理
export const createCompatibleIntersectionObserver = (
  callback: IntersectionObserverCallback,
  options?: IntersectionObserverInit
): IntersectionObserver | null => {
  if ('IntersectionObserver' in window) {
    return new IntersectionObserver(callback, options);
  } else {
    // 降级处理：使用滚动事件模拟
    console.warn('IntersectionObserver not supported, using scroll fallback');
    return null;
  }
};

// CSS自定义属性兼容性处理
export const setCSSCustomProperty = (property: string, value: string): void => {
  if (CSS.supports('color', 'var(--test)')) {
    document.documentElement.style.setProperty(property, value);
  } else {
    // 降级处理：直接设置样式
    console.warn('CSS custom properties not supported');
  }
};

// 响应式设计测试
export const testResponsiveBreakpoints = (): void => {
  const breakpoints = {
    mobile: 768,
    tablet: 1024,
    desktop: 1200
  };

  const currentWidth = window.innerWidth;
  let deviceType = 'desktop';

  if (currentWidth < breakpoints.mobile) {
    deviceType = 'mobile';
  } else if (currentWidth < breakpoints.tablet) {
    deviceType = 'tablet';
  }

  document.documentElement.classList.add(`device-${deviceType}`);
  
  // 在控制台输出当前设备类型
  console.log(`Current device type: ${deviceType} (${currentWidth}px)`);
};

// 性能监控
export const monitorPerformance = (): void => {
  if ('performance' in window && 'PerformanceObserver' in window) {
    // 监控最大内容绘制 (LCP)
    const lcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      if (process.env.NODE_ENV === 'production') {
        console.log('LCP:', lastEntry.startTime);
      }
    });
    lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

    // 监控首次输入延迟 (FID)
    const fidObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        if (process.env.NODE_ENV === 'production') {
          console.log('FID:', (entry as any).processingStart - entry.startTime);
        }
      });
    });
    fidObserver.observe({ entryTypes: ['first-input'] });

    // 监控累积布局偏移 (CLS)
    let clsValue = 0;
    const clsObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry: any) => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
        }
      });
      if (process.env.NODE_ENV === 'production') {
        console.log('CLS:', clsValue);
      }
    });
    clsObserver.observe({ entryTypes: ['layout-shift'] });
  }
};

// 初始化浏览器兼容性检测
export const initBrowserCompatibility = (): void => {
  addBrowserClasses();
  showBrowserWarning();
  testResponsiveBreakpoints();
  
  if (process.env.NODE_ENV === 'development') {
    monitorPerformance();
    
    // 输出浏览器信息
    const browserInfo = detectBrowser();
    console.log('Browser Info:', browserInfo);
  }
  
  // 监听窗口大小变化
  window.addEventListener('resize', testResponsiveBreakpoints);
};

export default {
  detectBrowser,
  addBrowserClasses,
  showBrowserWarning,
  createCompatibleAudioContext,
  createCompatibleIntersectionObserver,
  setCSSCustomProperty,
  testResponsiveBreakpoints,
  monitorPerformance,
  initBrowserCompatibility
};