// 跨浏览器测试工具

interface TestResult {
  name: string;
  passed: boolean;
  message: string;
  details?: any;
}

interface TestSuite {
  name: string;
  tests: TestResult[];
  passed: boolean;
}

// 基础功能测试
const testBasicFeatures = (): TestResult[] => {
  const tests: TestResult[] = [];

  // 测试 ES6 支持
  tests.push({
    name: 'ES6 Arrow Functions',
    passed: (() => {
      try {
        const test = () => true;
        return test();
      } catch {
        return false;
      }
    })(),
    message: 'ES6 箭头函数支持'
  });

  // 测试 Promise 支持
  tests.push({
    name: 'Promise Support',
    passed: typeof Promise !== 'undefined',
    message: 'Promise API 支持'
  });

  // 测试 Fetch API 支持
  tests.push({
    name: 'Fetch API',
    passed: typeof fetch !== 'undefined',
    message: 'Fetch API 支持'
  });

  // 测试 Local Storage 支持
  tests.push({
    name: 'Local Storage',
    passed: (() => {
      try {
        localStorage.setItem('test', 'test');
        localStorage.removeItem('test');
        return true;
      } catch {
        return false;
      }
    })(),
    message: 'Local Storage 支持'
  });

  return tests;
};

// CSS 功能测试
const testCSSFeatures = (): TestResult[] => {
  const tests: TestResult[] = [];

  // 测试 CSS Grid 支持
  tests.push({
    name: 'CSS Grid',
    passed: CSS.supports('display', 'grid'),
    message: 'CSS Grid 布局支持'
  });

  // 测试 CSS Flexbox 支持
  tests.push({
    name: 'CSS Flexbox',
    passed: CSS.supports('display', 'flex'),
    message: 'CSS Flexbox 布局支持'
  });

  // 测试 CSS 自定义属性支持
  tests.push({
    name: 'CSS Custom Properties',
    passed: CSS.supports('color', 'var(--test)'),
    message: 'CSS 自定义属性支持'
  });

  // 测试 CSS Transform 支持
  tests.push({
    name: 'CSS Transform',
    passed: CSS.supports('transform', 'translateX(10px)'),
    message: 'CSS Transform 支持'
  });

  // 测试 CSS Transition 支持
  tests.push({
    name: 'CSS Transition',
    passed: CSS.supports('transition', 'all 0.3s ease'),
    message: 'CSS Transition 支持'
  });

  return tests;
};

// 音频功能测试
const testAudioFeatures = (): TestResult[] => {
  const tests: TestResult[] = [];

  // 测试 HTML5 Audio 支持
  tests.push({
    name: 'HTML5 Audio',
    passed: (() => {
      const audio = document.createElement('audio');
      return !!(audio.canPlayType);
    })(),
    message: 'HTML5 Audio 支持'
  });

  // 测试 Web Audio API 支持
  tests.push({
    name: 'Web Audio API',
    passed: !!(window.AudioContext || (window as any).webkitAudioContext),
    message: 'Web Audio API 支持'
  });

  // 测试 MP3 格式支持
  tests.push({
    name: 'MP3 Support',
    passed: (() => {
      const audio = document.createElement('audio');
      return !!(audio.canPlayType && audio.canPlayType('audio/mpeg').replace(/no/, ''));
    })(),
    message: 'MP3 音频格式支持'
  });

  // 测试 OGG 格式支持
  tests.push({
    name: 'OGG Support',
    passed: (() => {
      const audio = document.createElement('audio');
      return !!(audio.canPlayType && audio.canPlayType('audio/ogg').replace(/no/, ''));
    })(),
    message: 'OGG 音频格式支持'
  });

  return tests;
};

// 响应式设计测试
const testResponsiveFeatures = (): TestResult[] => {
  const tests: TestResult[] = [];

  // 测试 Media Query 支持
  tests.push({
    name: 'Media Queries',
    passed: typeof window.matchMedia !== 'undefined',
    message: 'CSS Media Query 支持'
  });

  // 测试 Viewport Meta 标签
  tests.push({
    name: 'Viewport Meta',
    passed: (() => {
      const viewport = document.querySelector('meta[name="viewport"]');
      return !!viewport;
    })(),
    message: 'Viewport Meta 标签存在'
  });

  // 测试触摸事件支持
  tests.push({
    name: 'Touch Events',
    passed: 'ontouchstart' in window,
    message: '触摸事件支持'
  });

  // 测试设备像素比
  tests.push({
    name: 'Device Pixel Ratio',
    passed: typeof window.devicePixelRatio !== 'undefined',
    message: '设备像素比支持',
    details: { devicePixelRatio: window.devicePixelRatio }
  });

  return tests;
};

// 可访问性功能测试
const testAccessibilityFeatures = (): TestResult[] => {
  const tests: TestResult[] = [];

  // 测试 ARIA 支持
  tests.push({
    name: 'ARIA Support',
    passed: (() => {
      const div = document.createElement('div');
      div.setAttribute('aria-label', 'test');
      return div.getAttribute('aria-label') === 'test';
    })(),
    message: 'ARIA 属性支持'
  });

  // 测试键盘导航支持
  tests.push({
    name: 'Keyboard Navigation',
    passed: (() => {
      const div = document.createElement('div');
      div.tabIndex = 0;
      return div.tabIndex === 0;
    })(),
    message: '键盘导航支持'
  });

  // 测试 Focus 事件支持
  tests.push({
    name: 'Focus Events',
    passed: typeof document.addEventListener !== 'undefined',
    message: 'Focus 事件支持'
  });

  return tests;
};

// 性能相关测试
const testPerformanceFeatures = (): TestResult[] => {
  const tests: TestResult[] = [];

  // 测试 Performance API 支持
  tests.push({
    name: 'Performance API',
    passed: typeof performance !== 'undefined',
    message: 'Performance API 支持'
  });

  // 测试 Intersection Observer 支持
  tests.push({
    name: 'Intersection Observer',
    passed: 'IntersectionObserver' in window,
    message: 'Intersection Observer 支持'
  });

  // 测试 RequestAnimationFrame 支持
  tests.push({
    name: 'RequestAnimationFrame',
    passed: typeof requestAnimationFrame !== 'undefined',
    message: 'RequestAnimationFrame 支持'
  });

  // 测试 Web Workers 支持
  tests.push({
    name: 'Web Workers',
    passed: typeof Worker !== 'undefined',
    message: 'Web Workers 支持'
  });

  return tests;
};

// 运行所有测试
export const runCrossBrowserTests = (): TestSuite[] => {
  const testSuites: TestSuite[] = [
    {
      name: '基础功能测试',
      tests: testBasicFeatures(),
      passed: false
    },
    {
      name: 'CSS 功能测试',
      tests: testCSSFeatures(),
      passed: false
    },
    {
      name: '音频功能测试',
      tests: testAudioFeatures(),
      passed: false
    },
    {
      name: '响应式设计测试',
      tests: testResponsiveFeatures(),
      passed: false
    },
    {
      name: '可访问性测试',
      tests: testAccessibilityFeatures(),
      passed: false
    },
    {
      name: '性能功能测试',
      tests: testPerformanceFeatures(),
      passed: false
    }
  ];

  // 计算每个测试套件的通过状态
  testSuites.forEach(suite => {
    const passedTests = suite.tests.filter(test => test.passed).length;
    suite.passed = passedTests === suite.tests.length;
  });

  return testSuites;
};

// 生成测试报告
export const generateTestReport = (): string => {
  const testSuites = runCrossBrowserTests();
  let report = '=== 跨浏览器兼容性测试报告 ===\n\n';
  
  report += `浏览器信息: ${navigator.userAgent}\n`;
  report += `测试时间: ${new Date().toLocaleString()}\n\n`;

  testSuites.forEach(suite => {
    const passedTests = suite.tests.filter(test => test.passed).length;
    const totalTests = suite.tests.length;
    const passRate = ((passedTests / totalTests) * 100).toFixed(1);
    
    report += `${suite.name}: ${passedTests}/${totalTests} 通过 (${passRate}%)\n`;
    report += suite.passed ? '✅ 全部通过\n' : '❌ 存在问题\n';
    
    suite.tests.forEach(test => {
      const status = test.passed ? '✅' : '❌';
      report += `  ${status} ${test.name}: ${test.message}\n`;
      if (test.details) {
        report += `    详情: ${JSON.stringify(test.details)}\n`;
      }
    });
    
    report += '\n';
  });

  // 总体统计
  const totalTests = testSuites.reduce((sum, suite) => sum + suite.tests.length, 0);
  const totalPassed = testSuites.reduce((sum, suite) => 
    sum + suite.tests.filter(test => test.passed).length, 0);
  const overallPassRate = ((totalPassed / totalTests) * 100).toFixed(1);
  
  report += `=== 总体统计 ===\n`;
  report += `总测试数: ${totalTests}\n`;
  report += `通过数: ${totalPassed}\n`;
  report += `通过率: ${overallPassRate}%\n`;
  
  if (overallPassRate === '100.0') {
    report += '🎉 恭喜！所有测试都通过了！\n';
  } else {
    report += '⚠️  存在兼容性问题，建议检查失败的测试项。\n';
  }

  return report;
};

// 在控制台输出测试报告
export const logTestReport = (): void => {
  const report = generateTestReport();
  console.log(report);
};

// 自动运行测试（仅在开发环境）
export const autoRunTests = (): void => {
  if (process.env.NODE_ENV === 'development') {
    setTimeout(() => {
      logTestReport();
    }, 1000);
  }
};

export default {
  runCrossBrowserTests,
  generateTestReport,
  logTestReport,
  autoRunTests
};