import { getContrastRatio, meetsWCAGAA, meetsWCAGAAA } from './colorContrast';

// WCAG compliance validation utilities
export interface AccessibilityIssue {
  type: 'contrast' | 'focus' | 'aria' | 'keyboard' | 'structure';
  severity: 'error' | 'warning' | 'info';
  element?: HTMLElement;
  message: string;
  suggestion: string;
}

export interface ContrastValidationResult {
  passes: boolean;
  ratio: number;
  level: 'AA' | 'AAA' | 'fail';
  issues: AccessibilityIssue[];
}

/**
 * Validate color contrast for an element
 */
export const validateElementContrast = (element: HTMLElement): ContrastValidationResult => {
  const computedStyle = window.getComputedStyle(element);
  const color = computedStyle.color;
  const backgroundColor = computedStyle.backgroundColor;

  // Convert RGB to hex for contrast calculation
  const rgbToHex = (rgb: string): string => {
    const match = rgb.match(/\d+/g);
    if (!match) return '#000000';

    const [r, g, b] = match.map(Number);
    return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;
  };

  const foregroundHex = rgbToHex(color);
  const backgroundHex = rgbToHex(backgroundColor);

  const ratio = getContrastRatio(foregroundHex, backgroundHex);
  const fontSize = parseFloat(computedStyle.fontSize);
  const fontWeight = computedStyle.fontWeight;
  const isLargeText = fontSize >= 18 || (fontSize >= 14 && (fontWeight === 'bold' || parseInt(fontWeight) >= 700));

  const passesAA = meetsWCAGAA(foregroundHex, backgroundHex, isLargeText);
  const passesAAA = meetsWCAGAAA(foregroundHex, backgroundHex, isLargeText);

  const issues: AccessibilityIssue[] = [];

  if (!passesAA) {
    issues.push({
      type: 'contrast',
      severity: 'error',
      element,
      message: `颜色对比度不足 (${ratio.toFixed(2)}:1)`,
      suggestion: `需要至少 ${isLargeText ? '3:1' : '4.5:1'} 的对比度以符合 WCAG AA 标准`
    });
  } else if (!passesAAA) {
    issues.push({
      type: 'contrast',
      severity: 'warning',
      element,
      message: `颜色对比度未达到 AAA 级别 (${ratio.toFixed(2)}:1)`,
      suggestion: `建议达到 ${isLargeText ? '4.5:1' : '7:1'} 的对比度以符合 WCAG AAA 标准`
    });
  }

  return {
    passes: passesAA,
    ratio,
    level: passesAAA ? 'AAA' : passesAA ? 'AA' : 'fail',
    issues
  };
};

/**
 * Validate focus indicators for an element
 */
export const validateFocusIndicator = (element: HTMLElement): AccessibilityIssue[] => {
  const issues: AccessibilityIssue[] = [];
  const computedStyle = window.getComputedStyle(element, ':focus');

  const outline = computedStyle.outline;
  const outlineWidth = computedStyle.outlineWidth;
  const boxShadow = computedStyle.boxShadow;

  if (outline === 'none' && boxShadow === 'none') {
    issues.push({
      type: 'focus',
      severity: 'error',
      element,
      message: '缺少焦点指示器',
      suggestion: '添加 outline 或 box-shadow 来显示焦点状态'
    });
  } else if (parseFloat(outlineWidth) < 2) {
    issues.push({
      type: 'focus',
      severity: 'warning',
      element,
      message: '焦点指示器太细',
      suggestion: '建议使用至少 2px 的 outline 宽度'
    });
  }

  return issues;
};

/**
 * Validate ARIA attributes for an element
 */
export const validateAriaAttributes = (element: HTMLElement): AccessibilityIssue[] => {
  const issues: AccessibilityIssue[] = [];
  const tagName = element.tagName.toLowerCase();

  // Check for required ARIA labels on interactive elements
  const interactiveElements = ['button', 'a', 'input', 'select', 'textarea'];
  if (interactiveElements.includes(tagName) || element.getAttribute('role') === 'button') {
    const hasLabel = element.getAttribute('aria-label') ||
      element.getAttribute('aria-labelledby') ||
      element.textContent?.trim() ||
      (element as HTMLInputElement).placeholder;

    if (!hasLabel) {
      issues.push({
        type: 'aria',
        severity: 'error',
        element,
        message: '交互元素缺少可访问的名称',
        suggestion: '添加 aria-label, aria-labelledby 或可见文本'
      });
    }
  }

  // Check for proper heading hierarchy
  if (tagName.match(/^h[1-6]$/)) {
    const level = parseInt(tagName.charAt(1));
    const previousHeadings = Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6'))
      .filter(h => h.compareDocumentPosition(element) & Node.DOCUMENT_POSITION_PRECEDING);

    if (previousHeadings.length > 0) {
      const lastHeading = previousHeadings[previousHeadings.length - 1];
      const lastLevel = parseInt(lastHeading.tagName.charAt(1));

      if (level > lastLevel + 1) {
        issues.push({
          type: 'structure',
          severity: 'warning',
          element,
          message: '标题层级跳跃',
          suggestion: `从 h${lastLevel} 跳到 h${level}，建议使用连续的标题层级`
        });
      }
    }
  }

  return issues;
};

/**
 * Validate keyboard accessibility for an element
 */
export const validateKeyboardAccess = (element: HTMLElement): AccessibilityIssue[] => {
  const issues: AccessibilityIssue[] = [];
  const tabIndex = element.getAttribute('tabindex');
  const role = element.getAttribute('role');

  // Check if interactive elements are keyboard accessible
  const interactiveRoles = ['button', 'link', 'menuitem', 'tab', 'slider'];
  if (interactiveRoles.includes(role || '') || element.tagName.toLowerCase() === 'button') {
    if (tabIndex === '-1') {
      issues.push({
        type: 'keyboard',
        severity: 'warning',
        element,
        message: '交互元素不可通过键盘访问',
        suggestion: '移除 tabindex="-1" 或添加键盘事件处理'
      });
    }
  }

  return issues;
};

/**
 * Comprehensive accessibility audit for a container
 */
export const auditAccessibility = (container: HTMLElement = document.body): AccessibilityIssue[] => {
  const issues: AccessibilityIssue[] = [];

  // Get all elements with text content or interactive elements
  const elements = container.querySelectorAll('*');

  elements.forEach(element => {
    if (element instanceof HTMLElement) {
      // Validate contrast for elements with text
      if (element.textContent?.trim()) {
        const contrastResult = validateElementContrast(element);
        issues.push(...contrastResult.issues);
      }

      // Validate focus indicators for focusable elements
      if (element.tabIndex >= 0 || element.matches('button, a, input, select, textarea, [role="button"]')) {
        issues.push(...validateFocusIndicator(element));
      }

      // Validate ARIA attributes
      issues.push(...validateAriaAttributes(element));

      // Validate keyboard accessibility
      issues.push(...validateKeyboardAccess(element));
    }
  });

  return issues;
};

/**
 * Generate accessibility report
 */
export const generateAccessibilityReport = (container?: HTMLElement) => {
  const issues = auditAccessibility(container);

  const errorCount = issues.filter(issue => issue.severity === 'error').length;
  const warningCount = issues.filter(issue => issue.severity === 'warning').length;
  const infoCount = issues.filter(issue => issue.severity === 'info').length;

  const report = {
    summary: {
      total: issues.length,
      errors: errorCount,
      warnings: warningCount,
      info: infoCount,
      score: Math.max(0, 100 - (errorCount * 10) - (warningCount * 5) - (infoCount * 1))
    },
    issues: issues.sort((a, b) => {
      const severityOrder = { error: 3, warning: 2, info: 1 };
      return severityOrder[b.severity] - severityOrder[a.severity];
    })
  };

  return report;
};

/**
 * Log accessibility issues to console (development only)
 */
export const logAccessibilityIssues = (container?: HTMLElement) => {
  if (process.env.NODE_ENV !== 'development') return;

  const report = generateAccessibilityReport(container);

  console.group('🔍 Accessibility Audit Report');
  console.log(`Score: ${report.summary.score}/100`);
  console.log(`Issues: ${report.summary.errors} errors, ${report.summary.warnings} warnings, ${report.summary.info} info`);

  if (report.issues.length > 0) {
    report.issues.forEach(issue => {
      const icon = issue.severity === 'error' ? '❌' : issue.severity === 'warning' ? '⚠️' : 'ℹ️';
      console.log(`${icon} ${issue.message}`);
      console.log(`   💡 ${issue.suggestion}`);
      if (issue.element) {
        console.log('   📍 Element:', issue.element);
      }
    });
  } else {
    console.log('✅ No accessibility issues found!');
  }

  console.groupEnd();
};

export default {
  validateElementContrast,
  validateFocusIndicator,
  validateAriaAttributes,
  validateKeyboardAccess,
  auditAccessibility,
  generateAccessibilityReport,
  logAccessibilityIssues
};