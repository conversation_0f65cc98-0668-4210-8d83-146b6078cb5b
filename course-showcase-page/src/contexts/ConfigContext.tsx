import React, { createContext, useContext, useEffect } from 'react';
import type { ConfigContextType } from '../types';
import { useConfig } from '../hooks/useConfig';

const ConfigContext = createContext<ConfigContextType | undefined>(undefined);

interface ConfigProviderProps {
  children: React.ReactNode;
}

export const ConfigProvider: React.FC<ConfigProviderProps> = ({ children }) => {
  const { basicConfig, businessConfig, courseConfig, isLoading, error, refreshConfig } = useConfig();

  // Update document head with SEO information
  useEffect(() => {
    if (basicConfig?.seo) {
      const { seo } = basicConfig;
      
      // Update title
      document.title = seo.title;
      
      // Update meta description
      let metaDescription = document.querySelector('meta[name="description"]');
      if (!metaDescription) {
        metaDescription = document.createElement('meta');
        metaDescription.setAttribute('name', 'description');
        document.head.appendChild(metaDescription);
      }
      metaDescription.setAttribute('content', seo.description);
      
      // Update meta keywords
      let metaKeywords = document.querySelector('meta[name="keywords"]');
      if (!metaKeywords) {
        metaKeywords = document.createElement('meta');
        metaKeywords.setAttribute('name', 'keywords');
        document.head.appendChild(metaKeywords);
      }
      metaKeywords.setAttribute('content', seo.keywords);
      
      // Update Open Graph tags
      const ogTags = [
        { property: 'og:title', content: seo.ogTitle },
        { property: 'og:description', content: seo.ogDescription },
        { property: 'og:image', content: seo.ogImage },
        { property: 'og:type', content: 'website' }
      ];
      
      ogTags.forEach(({ property, content }) => {
        let ogTag = document.querySelector(`meta[property="${property}"]`);
        if (!ogTag) {
          ogTag = document.createElement('meta');
          ogTag.setAttribute('property', property);
          document.head.appendChild(ogTag);
        }
        ogTag.setAttribute('content', content);
      });
      
      // Update Twitter Card tags
      const twitterTags = [
        { name: 'twitter:card', content: 'summary_large_image' },
        { name: 'twitter:title', content: seo.ogTitle },
        { name: 'twitter:description', content: seo.ogDescription },
        { name: 'twitter:image', content: seo.ogImage }
      ];
      
      twitterTags.forEach(({ name, content }) => {
        let twitterTag = document.querySelector(`meta[name="${name}"]`);
        if (!twitterTag) {
          twitterTag = document.createElement('meta');
          twitterTag.setAttribute('name', name);
          document.head.appendChild(twitterTag);
        }
        twitterTag.setAttribute('content', content);
      });
    }
  }, [basicConfig?.seo]);

  const contextValue: ConfigContextType = {
    basicConfig,
    courseConfig,
    businessConfig,
    isLoading,
    error,
    refreshConfig
  };

  return (
    <ConfigContext.Provider value={contextValue}>
      {children}
    </ConfigContext.Provider>
  );
};

export const useConfigContext = (): ConfigContextType => {
  const context = useContext(ConfigContext);
  if (context === undefined) {
    throw new Error('useConfigContext must be used within a ConfigProvider');
  }
  return context;
};
