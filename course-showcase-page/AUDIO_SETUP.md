# 🎵 音频文件设置指南

## 快速开始

### 1. 准备音频文件
确保你有16个音频文件，按照课程顺序命名为：
- 01.mp3, 02.mp3, 03.mp3, ..., 16.mp3

### 2. 放置音频文件
将所有音频文件复制到以下目录：
```
course-showcase-page/public/audio/
```

### 3. 检查音频文件
运行检查脚本确认所有文件都正确放置：
```bash
npm run check-audio
```

### 4. 启动开发服务器
```bash
npm run dev
```

## 📋 完整的音频文件列表

| 文件名 | 模块 | 课程标题 | 预计时长 |
|--------|------|----------|----------|
| 01.mp3 | 理论篇 | 心理学的起源与发展 | 30分钟 |
| 02.mp3 | 理论篇 | 心理学主要流派简介 | 35分钟 |
| 03.mp3 | 理论篇 | 常见心理问题与特点速览 | 25分钟 |
| 04.mp3 | 心法篇 | 导论：你的过去，是最好的起点 | 20分钟 |
| 05.mp3 | 心法篇 | 重新定义"专业"：AI时代的咨询师新模型 | 30分钟 |
| 06.mp3 | 心法篇 | 建立边界感：从"热心肠"到"专业助人者" | 25分钟 |
| 07.mp3 | 技法篇 | 首次链接：如何30分钟建立信任感？ | 40分钟 |
| 08.mp3 | 技法篇 | 咨询过程：结构化咨询的"四步法" | 45分钟 |
| 09.mp3 | 技法篇 | 提问的艺术：成为"问题"的专家 | 35分钟 |
| 10.mp3 | 技法篇 | 收尾总结：让来访者带着希望离开的结构 | 30分钟 |
| 11.mp3 | AI篇 | 咨询前准备：AI帮你做案例预判和方案设计 | 35分钟 |
| 12.mp3 | AI篇 | 咨询中辅助：实时话术提醒和情绪识别 | 40分钟 |
| 13.mp3 | AI篇 | 咨询后复盘：AI协助案例分析和成长记录 | 30分钟 |
| 14.mp3 | 商业篇 | 如何找到前100个练手机会 | 45分钟 |
| 15.mp3 | 商业篇 | 定价策略和服务边界设定 | 35分钟 |
| 16.mp3 | 商业篇 | 危机干预和转介绍流程 | 30分钟 |

## 🔧 音频文件要求

- **格式**: MP3
- **质量**: 建议128kbps或更高
- **大小**: 建议每个文件不超过50MB
- **命名**: 严格按照01.mp3, 02.mp3...16.mp3命名

## 🚀 测试音频播放

1. 启动开发服务器：`npm run dev`
2. 打开浏览器访问本地地址
3. 点击任意课程章节
4. 验证音频能够正常播放

## ❓ 常见问题

### Q: 音频文件无法播放？
A: 检查以下几点：
- 文件是否放在正确的目录 (`public/audio/`)
- 文件名是否正确 (01.mp3, 02.mp3...)
- 文件格式是否为MP3
- 浏览器是否支持该音频格式

### Q: 如何更新音频文件的时长？
A: 编辑 `src/data/index.ts` 文件，更新对应课程的 `duration` 字段（以秒为单位）

### Q: 如何添加更多音频文件？
A: 
1. 在 `src/data/index.ts` 中添加新的课程数据
2. 将音频文件按顺序命名并放入 `public/audio/` 目录
3. 更新 `check-audio.js` 脚本中的映射表

## 📞 需要帮助？

如果遇到问题，请检查：
1. 控制台是否有错误信息
2. 网络请求是否成功
3. 音频文件是否可以直接在浏览器中访问 (如：http://localhost:5173/audio/01.mp3)